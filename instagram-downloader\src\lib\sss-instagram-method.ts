import axios from 'axios';

export class SSSInstagramMethod {
  
  // Método que replica exactamente cómo funciona sssinstagram.com
  static async downloadInstagramVideo(url: string) {
    console.log('🔥 Usando método de sssinstagram.com...');
    
    try {
      // Paso 1: Hacer petición a la API de sssinstagram
      const response = await axios.post('https://sssinstagram.com/request', 
        new URLSearchParams({
          url: url,
          lang: 'es'
        }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': '*/*',
          'X-Requested-With': 'XMLHttpRequest',
          'Origin': 'https://sssinstagram.com',
          'Referer': 'https://sssinstagram.com/es'
        },
        timeout: 30000
      });

      console.log('Respuesta de sssinstagram:', response.data);

      if (response.data) {
        const html = response.data;
        
        // Buscar enlaces de descarga en la respuesta
        const downloadMatches = html.match(/href="([^"]*)" download[^>]*>.*?(Descargar|Download)/gi);
        const videoMatches = html.match(/href="([^"]*\.mp4[^"]*)"/g);
        const thumbnailMatch = html.match(/src="([^"]*\.(jpg|jpeg|png)[^"]*)"/);
        
        if (downloadMatches && downloadMatches.length > 0) {
          const urlMatch = downloadMatches[0].match(/href="([^"]*)"/);
          if (urlMatch && urlMatch[1]) {
            console.log('✅ ¡ÉXITO con sssinstagram! Video encontrado:', urlMatch[1]);
            return {
              success: true,
              videoUrl: urlMatch[1],
              thumbnail: thumbnailMatch ? thumbnailMatch[1] : null,
              title: 'Video de Instagram descargado con sssinstagram'
            };
          }
        }
        
        if (videoMatches && videoMatches.length > 0) {
          const videoUrl = videoMatches[0].match(/href="([^"]*)"/)[1];
          console.log('✅ ¡ÉXITO con sssinstagram! Video encontrado:', videoUrl);
          return {
            success: true,
            videoUrl: videoUrl,
            thumbnail: thumbnailMatch ? thumbnailMatch[1] : null,
            title: 'Video de Instagram descargado con sssinstagram'
          };
        }
      }

      return { success: false };
    } catch (error) {
      console.error('❌ Error con sssinstagram:', error.message);
      return { success: false };
    }
  }

  // Método alternativo usando la API interna de sssinstagram
  static async downloadWithSSSAPI(url: string) {
    try {
      console.log('🔥 Usando API interna de sssinstagram...');
      
      // Primero obtener la página para conseguir tokens
      const pageResponse = await axios.get('https://sssinstagram.com/es', {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      // Buscar token CSRF si existe
      const csrfMatch = pageResponse.data.match(/name="_token" value="([^"]+)"/);
      const token = csrfMatch ? csrfMatch[1] : '';

      // Hacer petición a la API
      const response = await axios.post('https://sssinstagram.com/api/ajaxSearch', 
        new URLSearchParams({
          q: url,
          t: 'media',
          lang: 'es',
          _token: token
        }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'application/json, text/javascript, */*; q=0.01',
          'X-Requested-With': 'XMLHttpRequest',
          'Origin': 'https://sssinstagram.com',
          'Referer': 'https://sssinstagram.com/es'
        },
        timeout: 30000
      });

      console.log('Respuesta de API sssinstagram:', response.data);

      if (response.data && response.data.data) {
        const html = response.data.data;
        
        // Buscar enlaces de descarga
        const videoMatches = html.match(/href="([^"]*\.mp4[^"]*)"/g);
        const thumbnailMatch = html.match(/src="([^"]*\.(jpg|jpeg|png)[^"]*)"/);
        
        if (videoMatches && videoMatches.length > 0) {
          const videoUrl = videoMatches[0].match(/href="([^"]*)"/)[1];
          
          console.log('✅ ¡ÉXITO con API sssinstagram! Video encontrado:', videoUrl);
          return {
            success: true,
            videoUrl: videoUrl,
            thumbnail: thumbnailMatch ? thumbnailMatch[1] : null,
            title: 'Video de Instagram descargado con API sssinstagram'
          };
        }
      }

      return { success: false };
    } catch (error) {
      console.error('❌ Error con API sssinstagram:', error.message);
      return { success: false };
    }
  }

  // Método que usa SaveIG (otra página que funciona)
  static async downloadWithSaveIG(url: string) {
    try {
      console.log('🔥 Usando SaveIG...');
      
      const response = await axios.post('https://www.saveig.app/api/ajaxSearch', 
        new URLSearchParams({
          q: url,
          t: 'media',
          lang: 'en'
        }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': '*/*',
          'X-Requested-With': 'XMLHttpRequest',
          'Origin': 'https://www.saveig.app',
          'Referer': 'https://www.saveig.app/'
        },
        timeout: 30000
      });

      console.log('Respuesta de SaveIG:', response.data);

      if (response.data && response.data.data) {
        const html = response.data.data;
        
        // Buscar enlaces de descarga
        const videoMatches = html.match(/href="([^"]*\.mp4[^"]*)"/g);
        const downloadMatches = html.match(/download[^>]*href="([^"]*)"/g);
        const thumbnailMatch = html.match(/src="([^"]*\.(jpg|jpeg|png)[^"]*)"/);
        
        let videoUrl = null;
        
        if (downloadMatches && downloadMatches.length > 0) {
          const urlMatch = downloadMatches[0].match(/href="([^"]*)"/);
          videoUrl = urlMatch ? urlMatch[1] : null;
        } else if (videoMatches && videoMatches.length > 0) {
          videoUrl = videoMatches[0].match(/href="([^"]*)"/)[1];
        }
        
        if (videoUrl) {
          console.log('✅ ¡ÉXITO con SaveIG! Video encontrado:', videoUrl);
          return {
            success: true,
            videoUrl: videoUrl,
            thumbnail: thumbnailMatch ? thumbnailMatch[1] : null,
            title: 'Video de Instagram descargado con SaveIG'
          };
        }
      }

      return { success: false };
    } catch (error) {
      console.error('❌ Error con SaveIG:', error.message);
      return { success: false };
    }
  }

  // Método que usa SnapInsta (otra página confiable)
  static async downloadWithSnapInsta(url: string) {
    try {
      console.log('🔥 Usando SnapInsta...');
      
      const response = await axios.post('https://snapinsta.app/action.php', 
        new URLSearchParams({
          url: url,
          action: 'post'
        }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Origin': 'https://snapinsta.app',
          'Referer': 'https://snapinsta.app/'
        },
        timeout: 30000
      });

      console.log('Respuesta de SnapInsta recibida');

      if (response.data) {
        const html = response.data;
        
        // Buscar enlaces de descarga
        const downloadMatches = html.match(/href="([^"]*)" download[^>]*>.*?(Download|Descargar)/gi);
        const videoMatches = html.match(/href="([^"]*\.mp4[^"]*)"/g);
        
        let videoUrl = null;
        
        if (downloadMatches && downloadMatches.length > 0) {
          const urlMatch = downloadMatches[0].match(/href="([^"]*)"/);
          videoUrl = urlMatch ? urlMatch[1] : null;
        } else if (videoMatches && videoMatches.length > 0) {
          videoUrl = videoMatches[0].match(/href="([^"]*)"/)[1];
        }
        
        if (videoUrl && !videoUrl.includes('javascript:')) {
          console.log('✅ ¡ÉXITO con SnapInsta! Video encontrado:', videoUrl);
          return {
            success: true,
            videoUrl: videoUrl,
            thumbnail: null,
            title: 'Video de Instagram descargado con SnapInsta'
          };
        }
      }

      return { success: false };
    } catch (error) {
      console.error('❌ Error con SnapInsta:', error.message);
      return { success: false };
    }
  }

  // Método principal que prueba todos los servicios
  static async downloadRealInstagramVideo(url: string) {
    console.log('🚀 INICIANDO DESCARGA REAL DEL VIDEO DE INSTAGRAM...');
    
    const methods = [
      () => this.downloadInstagramVideo(url),
      () => this.downloadWithSSSAPI(url),
      () => this.downloadWithSaveIG(url),
      () => this.downloadWithSnapInsta(url)
    ];

    for (const method of methods) {
      try {
        const result = await method();
        if (result.success) {
          // Validar que la URL del video funciona
          const isValid = await this.validateVideoUrl(result.videoUrl);
          if (isValid) {
            console.log('✅ ¡VIDEO REAL DE INSTAGRAM ENCONTRADO Y VALIDADO!');
            return result;
          } else {
            console.log('❌ URL de video no válida, probando siguiente método...');
            continue;
          }
        }
      } catch (error) {
        console.log('Método falló, probando siguiente...');
        continue;
      }
    }

    console.log('❌ No se pudo extraer el video real de Instagram');
    return { success: false };
  }

  // Validar URLs de video
  static async validateVideoUrl(url: string): Promise<boolean> {
    try {
      const response = await axios.head(url, {
        timeout: 8000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15'
        },
        maxRedirects: 5
      });
      
      const isValidStatus = response.status >= 200 && response.status < 400;
      const isVideoContent = response.headers['content-type']?.includes('video') || 
                            response.headers['content-type']?.includes('application/octet-stream') ||
                            url.includes('.mp4');
      
      console.log(`Validación: ${url} - Status: ${response.status}, Content-Type: ${response.headers['content-type']}`);
      
      return isValidStatus && isVideoContent;
    } catch (error) {
      console.log(`Error validando: ${url} - ${error.message}`);
      return false;
    }
  }
}
