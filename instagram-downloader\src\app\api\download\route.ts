import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json();

    if (!url) {
      return NextResponse.json(
        { error: 'URL es requerida' },
        { status: 400 }
      );
    }

    // Validar que sea una URL de Instagram
    const instagramRegex = /^https?:\/\/(www\.)?(instagram\.com|instagr\.am)\/(p|reel|tv)\/[A-Za-z0-9_-]+/;
    if (!instagramRegex.test(url)) {
      return NextResponse.json(
        { error: 'URL de Instagram no válida' },
        { status: 400 }
      );
    }

    // Simular procesamiento
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Devolver respuesta de demostración
    return NextResponse.json({
      type: 'video',
      qualities: [
        {
          url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
          quality: 'HD (720p)',
        },
        {
          url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_640x360_1mb.mp4',
          quality: 'SD (360p)',
        }
      ],
      thumbnail: 'https://via.placeholder.com/400x400/e91e63/ffffff?text=Instagram+Video',
      caption: 'Video de demostración - InstaDownloader',
    });

  } catch (error: any) {
    console.error('Error en la API:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
