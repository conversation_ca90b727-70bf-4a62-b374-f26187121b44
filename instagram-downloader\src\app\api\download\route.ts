import { NextRequest, NextResponse } from 'next/server';
import { InstagramService } from '@/lib/instagram-service';

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json();

    if (!url) {
      return NextResponse.json(
        { error: 'URL es requerida' },
        { status: 400 }
      );
    }

    // Validar que sea una URL de Instagram
    if (!InstagramService.isValidInstagramUrl(url)) {
      return NextResponse.json(
        { error: 'URL de Instagram no válida' },
        { status: 400 }
      );
    }

    try {
      const videoData = await InstagramService.getVideoData(url);
      return NextResponse.json(videoData);
    } catch (error: any) {
      console.error('Error al obtener datos de Instagram:', error.message);

      return NextResponse.json(
        { error: error.message || 'No se pudo acceder al contenido de Instagram. El video podría ser privado o no estar disponible.' },
        { status: 503 }
      );
    }

  } catch (error: any) {
    console.error('Error en la API:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
