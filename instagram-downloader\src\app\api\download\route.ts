import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import { WorkingDownloader } from '@/lib/working-downloader';

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json();

    if (!url) {
      return NextResponse.json(
        { error: 'URL es requerida' },
        { status: 400 }
      );
    }

    // Validar que sea una URL de Instagram
    const instagramRegex = /^https?:\/\/(www\.)?(instagram\.com|instagr\.am)\/(p|reel|tv)\/[A-Za-z0-9_-]+/;
    if (!instagramRegex.test(url)) {
      return NextResponse.json(
        { error: 'URL de Instagram no válida' },
        { status: 400 }
      );
    }

    // Limpiar la URL
    const cleanUrl = url.split('?')[0];

    try {
      // 🔥 MÉTODO QUE REALMENTE FUNCIONA
      console.log('🔥 INICIANDO DESCARGA CON MÉTODOS QUE FUNCIONAN...');
      const result = await WorkingDownloader.downloadInstagramVideo(cleanUrl);

      if (result.success) {
        // Obtener información del archivo
        const fileInfo = await WorkingDownloader.getFileInfo(result.videoUrl);

        console.log('✅ ¡VIDEO DESCARGADO EXITOSAMENTE!');
        return NextResponse.json({
          type: 'video',
          qualities: [{
            url: result.videoUrl,
            quality: fileInfo.isValid ? `HD (${fileInfo.size})` : 'HD',
          }],
          thumbnail: result.thumbnail,
          caption: result.title
        });
      }

      return NextResponse.json(
        { error: 'No se pudo extraer el video. El contenido podría ser privado o no estar disponible.' },
        { status: 404 }
      );

    } catch (error: any) {
      console.error('Error al procesar video:', error);
      return NextResponse.json(
        { error: 'Error al procesar el video. Inténtalo de nuevo.' },
        { status: 503 }
      );
    }

  } catch (error: any) {
    console.error('Error en la API:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}


