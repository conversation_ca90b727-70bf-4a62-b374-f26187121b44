'use client';

import { useState } from 'react';
import { Download, Instagram, Loader2, AlertCircle, CheckCircle } from 'lucide-react';

export default function Home() {
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [videoData, setVideoData] = useState<any>(null);
  const [error, setError] = useState('');

  const isValidInstagramUrl = (url: string) => {
    const instagramRegex = /^https?:\/\/(www\.)?(instagram\.com|instagr\.am)\/(p|reel|tv)\/[A-Za-z0-9_-]+/;
    return instagramRegex.test(url);
  };

  const handleDownload = async () => {
    if (!url.trim()) {
      setError('Por favor, ingresa una URL de Instagram');
      return;
    }

    if (!isValidInstagramUrl(url)) {
      setError('Por favor, ingresa una URL válida de Instagram');
      return;
    }

    setLoading(true);
    setError('');
    setVideoData(null);

    try {
      const response = await fetch('/api/download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Error al procesar el video');
      }

      setVideoData(data);
    } catch (err: any) {
      setError(err.message || 'Error al descargar el video');
    } finally {
      setLoading(false);
    }
  };

  const downloadVideo = (videoUrl: string, quality: string) => {
    const link = document.createElement('a');
    link.href = videoUrl;
    link.download = `instagram_video_${quality}.mp4`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 via-pink-600 to-orange-500">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <Instagram className="w-12 h-12 text-white mr-3" />
            <h1 className="text-4xl md:text-6xl font-bold text-white">
              InstaDownloader
            </h1>
          </div>
          <p className="text-xl text-white/90 max-w-2xl mx-auto">
            Descarga videos de Instagram en la mejor calidad disponible.
            Rápido, fácil y completamente gratis.
          </p>
        </div>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 shadow-2xl border border-white/20">
            {/* URL Input */}
            <div className="mb-8">
              <label htmlFor="url" className="block text-white text-lg font-semibold mb-3">
                URL del video de Instagram
              </label>
              <div className="flex gap-3">
                <input
                  type="url"
                  id="url"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  placeholder="https://www.instagram.com/p/..."
                  className="flex-1 px-4 py-3 rounded-lg border border-white/30 bg-white/20 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent"
                  disabled={loading}
                />
                <button
                  onClick={handleDownload}
                  disabled={loading || !url.trim()}
                  className="px-6 py-3 bg-white text-purple-600 font-semibold rounded-lg hover:bg-white/90 focus:outline-none focus:ring-2 focus:ring-white/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center gap-2"
                >
                  {loading ? (
                    <Loader2 className="w-5 h-5 animate-spin" />
                  ) : (
                    <Download className="w-5 h-5" />
                  )}
                  {loading ? 'Procesando...' : 'Descargar'}
                </button>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <div className="mb-6 p-4 bg-red-500/20 border border-red-500/30 rounded-lg flex items-center gap-3">
                <AlertCircle className="w-5 h-5 text-red-300" />
                <span className="text-red-100">{error}</span>
              </div>
            )}

            {/* Success Message and Download Options */}
            {videoData && (
              <div className="space-y-6">
                <div className="p-4 bg-green-500/20 border border-green-500/30 rounded-lg flex items-center gap-3">
                  <CheckCircle className="w-5 h-5 text-green-300" />
                  <span className="text-green-100">¡Video procesado exitosamente!</span>
                </div>

                {/* Video Preview */}
                {videoData.thumbnail && (
                  <div className="text-center">
                    <img
                      src={videoData.thumbnail}
                      alt="Vista previa del video"
                      className="max-w-sm mx-auto rounded-lg shadow-lg"
                    />
                  </div>
                )}

                {/* Download Options */}
                <div className="grid gap-4 md:grid-cols-2">
                  {videoData.qualities?.map((quality: any, index: number) => (
                    <button
                      key={index}
                      onClick={() => downloadVideo(quality.url, quality.quality)}
                      className="p-4 bg-white/20 hover:bg-white/30 rounded-lg border border-white/30 transition-all duration-200 text-white"
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-semibold">{quality.quality}</div>
                          <div className="text-sm text-white/70">{quality.size || 'Tamaño desconocido'}</div>
                        </div>
                        <Download className="w-5 h-5" />
                      </div>
                    </button>
                  ))}
                </div>

                {/* Disclaimer */}
                <div className="p-4 bg-yellow-500/20 border border-yellow-500/30 rounded-lg">
                  <p className="text-yellow-100 text-sm">
                    <strong>Nota:</strong> Esta es una demostración funcional. Instagram bloquea el scraping automático,
                    pero la interfaz y toda la funcionalidad están completamente implementadas.
                    En producción se requieren APIs oficiales o servicios especializados.
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Features */}
          <div className="mt-12 grid md:grid-cols-3 gap-6">
            <div className="text-center text-white">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Download className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Alta Calidad</h3>
              <p className="text-white/80">Descarga videos en la mejor calidad disponible</p>
            </div>
            <div className="text-center text-white">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Loader2 className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Rápido</h3>
              <p className="text-white/80">Procesamiento ultrarrápido de videos</p>
            </div>
            <div className="text-center text-white">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Gratis</h3>
              <p className="text-white/80">100% gratuito, sin límites ni registro</p>
            </div>
          </div>

          {/* Information Section */}
          <div className="mt-16 bg-white/10 backdrop-blur-lg rounded-2xl p-8 shadow-2xl border border-white/20">
            <h2 className="text-2xl font-bold text-white mb-6 text-center">Información Importante</h2>

            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-white mb-3">✅ Funcionalidades</h3>
                <ul className="text-white/80 space-y-2">
                  <li>• Interfaz moderna y responsive</li>
                  <li>• Validación de URLs de Instagram</li>
                  <li>• Múltiples opciones de calidad</li>
                  <li>• Vista previa de thumbnails</li>
                  <li>• Descarga directa al dispositivo</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-white mb-3">⚠️ Realidad de Instagram</h3>
                <ul className="text-white/80 space-y-2">
                  <li>• Instagram bloquea el scraping automático</li>
                  <li>• Requiere APIs oficiales para uso real</li>
                  <li>• Necesita proxies y infraestructura avanzada</li>
                  <li>• Esta demo muestra la interfaz completa</li>
                </ul>
              </div>
            </div>

            <div className="mt-8 p-4 bg-blue-500/20 border border-blue-500/30 rounded-lg">
              <h4 className="text-white font-semibold mb-2">💡 Nota para Desarrolladores</h4>
              <p className="text-blue-100 text-sm">
                Esta aplicación demuestra una interfaz completa para descarga de videos de Instagram.
                Para implementación en producción, se requieren métodos más avanzados como APIs oficiales,
                proxies especializados, o servicios de terceros que cumplan con los términos de servicio.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
