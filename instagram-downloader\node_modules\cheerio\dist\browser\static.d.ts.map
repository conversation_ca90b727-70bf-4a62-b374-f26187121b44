{"version": 3, "file": "static.d.ts", "sourceRoot": "", "sources": ["../../src/static.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,kBAAkB,EAAE,MAAM,YAAY,CAAC;AACrD,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,WAAW,CAAC;AAC5C,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AAC5C,OAAO,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAEpD,OAAO,EAEL,KAAK,cAAc,EAEpB,MAAM,cAAc,CAAC;AACtB,OAAO,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAwCjE;;;;;;GAMG;AACH,wBAAgB,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,EAAE,cAAc,GAAG,MAAM,CAAC;AACzE;;;;;;;GAOG;AACH,wBAAgB,IAAI,CAClB,IAAI,EAAE,UAAU,EAChB,GAAG,CAAC,EAAE,kBAAkB,CAAC,OAAO,CAAC,EACjC,OAAO,CAAC,EAAE,cAAc,GACvB,MAAM,CAAC;AA0BV;;;;;;GAMG;AACH,wBAAgB,GAAG,CACjB,IAAI,EAAE,UAAU,EAChB,GAAG,CAAC,EAAE,kBAAkB,CAAC,OAAO,CAAC,GAChC,MAAM,CAIR;AAED;;;;;;;;;;GAUG;AACH,wBAAgB,IAAI,CAClB,IAAI,EAAE,UAAU,GAAG,IAAI,EACvB,QAAQ,CAAC,EAAE,SAAS,CAAC,OAAO,CAAC,GAC5B,MAAM,CAUR;AAED;;;;;;;;;;;;GAYG;AACH,wBAAgB,SAAS,CACvB,IAAI,EAAE,UAAU,EAChB,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE,OAAO,EACjB,WAAW,CAAC,EAAE,OAAO,GACpB,OAAO,EAAE,CAAC;AACb,wBAAgB,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;AA8BpE;;;;;;;;;;;;;;GAcG;AACH,wBAAgB,IAAI,CAAC,IAAI,EAAE,UAAU,GAAG,OAAO,CAAC,QAAQ,CAAC,CAExD;AAED;;;;;;;;;;GAUG;AACH,wBAAgB,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,GAAG,OAAO,CAmBxE;AAED;;;;;;;;GAQG;AACH,wBAAgB,OAAO,CAAC,CAAC,SAAS,UAAU,EAC1C,IAAI,EAAE,UAAU,EAChB,GAAG,EAAE,CAAC,GACL,YAAY,CAAC,CAAC,CAAC,CAEjB;AAED,KAAK,QAAQ,CAAC,CAAC,IAAI;IAAE,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CAAE,CAAC;AAEtD;;;;;;;;;GASG;AACH,wBAAgB,KAAK,CAAC,CAAC,EACrB,IAAI,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAC5B,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,GACjB,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAY1B"}