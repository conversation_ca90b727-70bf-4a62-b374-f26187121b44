import axios from 'axios';

export class AdvancedInstagramExtractor {
  
  static async extractVideoData(url: string) {
    console.log('🔥 Iniciando extracción avanzada...');
    
    // Método 1: Usar API de InstagramDP (funciona sin key)
    try {
      const response = await axios.post('https://instagramdp.com/api/instagram', {
        url: url
      }, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'application/json'
        },
        timeout: 15000
      });

      if (response.data && response.data.video_url) {
        return {
          success: true,
          videoUrl: response.data.video_url,
          thumbnail: response.data.thumbnail_url,
          title: 'Video de Instagram'
        };
      }
    } catch (error) {
      console.log('InstagramDP falló:', error.message);
    }

    // Método 2: Usar técnica de embed
    try {
      const shortcode = this.extractShortcode(url);
      if (shortcode) {
        const embedUrl = `https://www.instagram.com/p/${shortcode}/embed/`;
        
        const response = await axios.get(embedUrl, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15'
          },
          timeout: 15000
        });

        const html = response.data;
        const videoMatch = html.match(/"video_url":"([^"]+)"/);
        
        if (videoMatch) {
          const videoUrl = videoMatch[1].replace(/\\u0026/g, '&').replace(/\\/g, '');
          return {
            success: true,
            videoUrl: videoUrl,
            thumbnail: null,
            title: 'Video extraído del embed'
          };
        }
      }
    } catch (error) {
      console.log('Método embed falló:', error.message);
    }

    // Método 3: Usar API de Insta-Save (alternativa)
    try {
      const response = await axios.get(`https://insta-save.net/download?url=${encodeURIComponent(url)}`, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        },
        timeout: 15000
      });

      const html = response.data;
      const videoMatch = html.match(/href="([^"]*\.mp4[^"]*)"/);
      
      if (videoMatch && videoMatch[1]) {
        return {
          success: true,
          videoUrl: videoMatch[1],
          thumbnail: null,
          title: 'Video de Insta-Save'
        };
      }
    } catch (error) {
      console.log('Insta-Save falló:', error.message);
    }

    // Método 4: Técnica de GraphQL (avanzada)
    try {
      const shortcode = this.extractShortcode(url);
      if (shortcode) {
        const graphqlUrl = 'https://www.instagram.com/graphql/query/';
        
        const response = await axios.post(graphqlUrl, {
          query_hash: 'b3055c01b4b222b8a47dc12b090e4e64',
          variables: JSON.stringify({
            shortcode: shortcode,
            child_comment_count: 3,
            fetch_comment_count: 40,
            parent_comment_count: 24,
            has_threaded_comments: true
          })
        }, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15',
            'X-Requested-With': 'XMLHttpRequest'
          },
          timeout: 15000
        });

        if (response.data && response.data.data && response.data.data.shortcode_media) {
          const media = response.data.data.shortcode_media;
          if (media.is_video && media.video_url) {
            return {
              success: true,
              videoUrl: media.video_url,
              thumbnail: media.display_url,
              title: media.edge_media_to_caption?.edges?.[0]?.node?.text || 'Video de Instagram'
            };
          }
        }
      }
    } catch (error) {
      console.log('GraphQL falló:', error.message);
    }

    // Método 5: Usar múltiples proxies
    try {
      const proxies = [
        'https://api.allorigins.win/get?url=',
        'https://cors-anywhere.herokuapp.com/',
        'https://thingproxy.freeboard.io/fetch/'
      ];

      for (const proxy of proxies) {
        try {
          const proxyUrl = proxy + encodeURIComponent(url);
          const response = await axios.get(proxyUrl, {
            headers: {
              'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15'
            },
            timeout: 10000
          });

          let html = response.data;
          if (typeof html === 'object' && html.contents) {
            html = html.contents;
          }

          const patterns = [
            /"video_url":"([^"]+)"/,
            /"playback_url":"([^"]+)"/,
            /"src":"([^"]*\.mp4[^"]*)"/
          ];

          for (const pattern of patterns) {
            const match = html.match(pattern);
            if (match && match[1]) {
              const videoUrl = match[1].replace(/\\u0026/g, '&').replace(/\\/g, '');
              if (videoUrl.includes('.mp4')) {
                return {
                  success: true,
                  videoUrl: videoUrl,
                  thumbnail: null,
                  title: 'Video extraído con proxy'
                };
              }
            }
          }
        } catch (proxyError) {
          continue;
        }
      }
    } catch (error) {
      console.log('Proxies fallaron:', error.message);
    }

    return { success: false };
  }

  private static extractShortcode(url: string): string | null {
    const match = url.match(/\/(p|reel|tv)\/([A-Za-z0-9_-]+)/);
    return match ? match[2] : null;
  }

  // Método para generar URLs de diferentes calidades
  static generateQualityUrls(baseUrl: string) {
    const qualities = [];
    
    // Intentar diferentes resoluciones comunes de Instagram
    const resolutions = [
      { width: 1080, height: 1920, quality: 'HD (1080p)' },
      { width: 720, height: 1280, quality: 'HD (720p)' },
      { width: 480, height: 854, quality: 'SD (480p)' },
      { width: 360, height: 640, quality: 'SD (360p)' }
    ];

    for (const res of resolutions) {
      // Intentar modificar la URL para obtener diferentes calidades
      let qualityUrl = baseUrl;
      
      // Algunos patrones comunes de URLs de Instagram
      if (baseUrl.includes('_n.mp4')) {
        qualityUrl = baseUrl.replace('_n.mp4', `_${res.width}.mp4`);
      } else if (baseUrl.includes('.mp4')) {
        qualityUrl = baseUrl.replace('.mp4', `_${res.width}.mp4`);
      }

      qualities.push({
        url: qualityUrl,
        quality: res.quality,
        width: res.width,
        height: res.height
      });
    }

    // Si no se pueden generar variaciones, devolver la original
    if (qualities.length === 0) {
      qualities.push({
        url: baseUrl,
        quality: 'Original',
        width: 0,
        height: 0
      });
    }

    return qualities;
  }

  // Método para validar si una URL de video funciona
  static async validateVideoUrl(url: string): Promise<boolean> {
    try {
      const response = await axios.head(url, {
        timeout: 5000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15'
        }
      });
      
      return response.status === 200 && 
             response.headers['content-type']?.includes('video');
    } catch (error) {
      return false;
    }
  }
}
