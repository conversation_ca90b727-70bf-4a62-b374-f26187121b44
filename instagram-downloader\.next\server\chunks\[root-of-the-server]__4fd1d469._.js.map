{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/videostiktok/instagram-downloader/src/app/api/download/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { url } = await request.json();\n\n    if (!url) {\n      return NextResponse.json(\n        { error: 'URL es requerida' },\n        { status: 400 }\n      );\n    }\n\n    // Validar que sea una URL de Instagram\n    const instagramRegex = /^https?:\\/\\/(www\\.)?(instagram\\.com|instagr\\.am)\\/(p|reel|tv)\\/[A-Za-z0-9_-]+/;\n    if (!instagramRegex.test(url)) {\n      return NextResponse.json(\n        { error: 'URL de Instagram no válida' },\n        { status: 400 }\n      );\n    }\n\n    // Simular procesamiento\n    await new Promise(resolve => setTimeout(resolve, 2000));\n\n    // Devolver respuesta de demostración\n    return NextResponse.json({\n      type: 'video',\n      qualities: [\n        {\n          url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',\n          quality: 'HD (720p)',\n        },\n        {\n          url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_640x360_1mb.mp4',\n          quality: 'SD (360p)',\n        }\n      ],\n      thumbnail: 'https://via.placeholder.com/400x400/e91e63/ffffff?text=Instagram+Video',\n      caption: 'Video de demostración - InstaDownloader',\n    });\n\n  } catch (error: any) {\n    console.error('Error en la API:', error);\n    return NextResponse.json(\n      { error: 'Error interno del servidor' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,QAAQ,IAAI;QAElC,IAAI,CAAC,KAAK;YACR,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,uCAAuC;QACvC,MAAM,iBAAiB;QACvB,IAAI,CAAC,eAAe,IAAI,CAAC,MAAM;YAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6B,GACtC;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,qCAAqC;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,MAAM;YACN,WAAW;gBACT;oBACE,KAAK;oBACL,SAAS;gBACX;gBACA;oBACE,KAAK;oBACL,SAAS;gBACX;aACD;YACD,WAAW;YACX,SAAS;QACX;IAEF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA6B,GACtC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}