import axios from 'axios';
import puppeteer from 'puppeteer';

export class CustomInstagramAPI {
  
  // Método principal que extrae el video REAL de Instagram
  static async extractInstagramVideo(url: string) {
    console.log('🔥 INICIANDO EXTRACCIÓN PERSONALIZADA DE INSTAGRAM...');
    console.log('URL a procesar:', url);
    
    try {
      // Limpiar la URL
      const cleanUrl = this.cleanInstagramUrl(url);
      console.log('URL limpia:', cleanUrl);
      
      // Método 1: Extracción directa con múltiples user agents
      const directResult = await this.extractWithDirectAccess(cleanUrl);
      if (directResult.success) return directResult;
      
      // Método 2: Usar embed de Instagram
      const embedResult = await this.extractWithEmbed(cleanUrl);
      if (embedResult.success) return embedResult;
      
      // Método 3: Usar técnica de oembed
      const oembedResult = await this.extractWithOembed(cleanUrl);
      if (oembedResult.success) return oembedResult;
      
      // Método 4: Puppeteer (navegador real)
      const puppeteerResult = await this.extractWithPuppeteer(cleanUrl);
      if (puppeteerResult.success) return puppeteerResult;

      // Método 5: API GraphQL directa
      const graphqlResult = await this.extractWithGraphQL(cleanUrl);
      if (graphqlResult.success) return graphqlResult;

      return { success: false, error: 'No se pudo extraer el video' };
      
    } catch (error) {
      console.error('Error en extracción:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Limpiar URL de Instagram
  static cleanInstagramUrl(url: string): string {
    // Remover parámetros de tracking
    let cleanUrl = url.split('?')[0];
    
    // Asegurar que termine con /
    if (!cleanUrl.endsWith('/')) {
      cleanUrl += '/';
    }
    
    return cleanUrl;
  }
  
  // Método 1: Acceso directo con headers específicos
  static async extractWithDirectAccess(url: string) {
    console.log('🎯 Método 1: Acceso directo...');
    
    const userAgents = [
      'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
      'Mozilla/5.0 (Android 12; Mobile; rv:95.0) Gecko/95.0 Firefox/95.0',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Instagram **********.117 Android (29/10; 480dpi; 1080x2340; samsung; SM-G975F; beyond2; exynos9820; en_US; 336448914)'
    ];
    
    for (const userAgent of userAgents) {
      try {
        console.log(`Probando con User-Agent: ${userAgent.substring(0, 50)}...`);
        
        const response = await axios.get(url, {
          headers: {
            'User-Agent': userAgent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
          },
          timeout: 20000,
          maxRedirects: 5
        });
        
        const html = response.data;
        console.log(`HTML recibido: ${html.length} caracteres`);
        
        // Buscar datos JSON embebidos
        const videoData = this.extractVideoFromHTML(html);
        if (videoData) {
          console.log('✅ Video encontrado con acceso directo!');
          return {
            success: true,
            videoUrl: videoData.videoUrl,
            thumbnail: videoData.thumbnail,
            title: videoData.title
          };
        }
        
      } catch (error) {
        console.log(`Error con User-Agent ${userAgent.substring(0, 30)}: ${error.message}`);
        continue;
      }
    }
    
    return { success: false };
  }
  
  // Método 2: Usar embed de Instagram
  static async extractWithEmbed(url: string) {
    console.log('🎯 Método 2: Embed de Instagram...');
    
    try {
      const embedUrl = `${url}embed/`;
      console.log('URL de embed:', embedUrl);
      
      const response = await axios.get(embedUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Referer': 'https://www.instagram.com/'
        },
        timeout: 20000
      });
      
      const html = response.data;
      console.log(`HTML de embed recibido: ${html.length} caracteres`);
      
      const videoData = this.extractVideoFromHTML(html);
      if (videoData) {
        console.log('✅ Video encontrado con embed!');
        return {
          success: true,
          videoUrl: videoData.videoUrl,
          thumbnail: videoData.thumbnail,
          title: videoData.title
        };
      }
      
    } catch (error) {
      console.log('Error con embed:', error.message);
    }
    
    return { success: false };
  }
  
  // Método 3: Usar oembed de Instagram
  static async extractWithOembed(url: string) {
    console.log('🎯 Método 3: oEmbed de Instagram...');
    
    try {
      const oembedUrl = `https://api.instagram.com/oembed/?url=${encodeURIComponent(url)}`;
      console.log('URL de oembed:', oembedUrl);
      
      const response = await axios.get(oembedUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'application/json'
        },
        timeout: 15000
      });
      
      console.log('Respuesta de oembed:', response.data);
      
      if (response.data && response.data.html) {
        const html = response.data.html;
        const videoData = this.extractVideoFromHTML(html);
        
        if (videoData) {
          console.log('✅ Video encontrado con oembed!');
          return {
            success: true,
            videoUrl: videoData.videoUrl,
            thumbnail: videoData.thumbnail,
            title: response.data.title || videoData.title
          };
        }
      }
      
    } catch (error) {
      console.log('Error con oembed:', error.message);
    }
    
    return { success: false };
  }
  
  // Método 4: Puppeteer (navegador real)
  static async extractWithPuppeteer(url: string) {
    console.log('🎯 Método 4: Puppeteer (navegador real)...');

    let browser = null;
    try {
      // Lanzar navegador
      browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      const page = await browser.newPage();

      // Configurar user agent móvil
      await page.setUserAgent('Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1');

      // Configurar viewport móvil
      await page.setViewport({ width: 375, height: 667 });

      console.log('Navegando a:', url);

      // Navegar a la página
      await page.goto(url, {
        waitUntil: 'networkidle2',
        timeout: 30000
      });

      // Esperar un poco para que cargue el contenido
      await page.waitForTimeout(3000);

      // Buscar elementos de video
      const videoData = await page.evaluate(() => {
        // Buscar elementos video
        const videoElements = document.querySelectorAll('video');
        for (const video of videoElements) {
          if (video.src && video.src.includes('.mp4')) {
            return {
              videoUrl: video.src,
              thumbnail: video.poster || null,
              title: document.title || 'Video de Instagram'
            };
          }
        }

        // Buscar en el HTML
        const html = document.documentElement.innerHTML;

        const patterns = [
          /"video_url":"([^"]+)"/,
          /"playback_url":"([^"]+)"/,
          /"video_versions":\[{"url":"([^"]+)"/,
          /"src":"([^"]*\.mp4[^"]*)"/
        ];

        for (const pattern of patterns) {
          const match = html.match(pattern);
          if (match && match[1]) {
            let videoUrl = match[1].replace(/\\u0026/g, '&').replace(/\\/g, '');

            if (videoUrl && videoUrl.includes('.mp4') && !videoUrl.includes('blob:') && videoUrl.startsWith('http')) {
              // Buscar thumbnail
              const thumbMatch = html.match(/"display_url":"([^"]+)"/);
              const thumbnail = thumbMatch ? thumbMatch[1].replace(/\\/g, '') : null;

              return {
                videoUrl,
                thumbnail,
                title: document.title || 'Video de Instagram'
              };
            }
          }
        }

        return null;
      });

      if (videoData) {
        console.log('✅ Video encontrado con Puppeteer!');
        return {
          success: true,
          videoUrl: videoData.videoUrl,
          thumbnail: videoData.thumbnail,
          title: videoData.title
        };
      }

    } catch (error) {
      console.log('Error con Puppeteer:', error.message);
    } finally {
      if (browser) {
        await browser.close();
      }
    }

    return { success: false };
  }

  // Método 5: GraphQL directo
  static async extractWithGraphQL(url: string) {
    console.log('🎯 Método 4: GraphQL directo...');
    
    try {
      // Extraer shortcode de la URL
      const shortcodeMatch = url.match(/\/(p|reel|tv)\/([A-Za-z0-9_-]+)/);
      if (!shortcodeMatch) {
        console.log('No se pudo extraer shortcode');
        return { success: false };
      }
      
      const shortcode = shortcodeMatch[2];
      console.log('Shortcode extraído:', shortcode);
      
      // Usar GraphQL de Instagram
      const graphqlUrl = 'https://www.instagram.com/graphql/query/';
      const queryHash = 'b3055c01b4b222b8a47dc12b090e4e64';
      
      const response = await axios.post(graphqlUrl, 
        new URLSearchParams({
          query_hash: queryHash,
          variables: JSON.stringify({
            shortcode: shortcode,
            child_comment_count: 3,
            fetch_comment_count: 40,
            parent_comment_count: 24,
            has_threaded_comments: true
          })
        }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15',
          'X-Requested-With': 'XMLHttpRequest',
          'X-IG-App-ID': '936619743392459',
          'Accept': 'application/json',
          'Referer': 'https://www.instagram.com/'
        },
        timeout: 20000
      });
      
      console.log('Respuesta de GraphQL:', response.data);
      
      if (response.data && response.data.data && response.data.data.shortcode_media) {
        const media = response.data.data.shortcode_media;
        
        if (media.is_video && media.video_url) {
          console.log('✅ Video encontrado con GraphQL!');
          return {
            success: true,
            videoUrl: media.video_url,
            thumbnail: media.display_url,
            title: media.edge_media_to_caption?.edges?.[0]?.node?.text || 'Video de Instagram'
          };
        }
      }
      
    } catch (error) {
      console.log('Error con GraphQL:', error.message);
    }
    
    return { success: false };
  }
  
  // Extraer video del HTML
  static extractVideoFromHTML(html: string) {
    console.log('🔍 Analizando HTML para extraer video...');
    
    // Patrones para buscar videos
    const patterns = [
      /"video_url":"([^"]+)"/,
      /"playback_url":"([^"]+)"/,
      /"video_versions":\[{"url":"([^"]+)"/,
      /"src":"([^"]*\.mp4[^"]*)"/,
      /videoUrl":"([^"]+)"/,
      /"contentUrl":"([^"]+\.mp4[^"]*)"/,
      /video_url\\?":\\?"([^"]+)\\?"/,
      /"VideoList":\[{"type":"video\/mp4","src":"([^"]+)"/,
      /"video_resources":\[{"src":"([^"]+)"/,
      /data-video-url="([^"]+)"/,
      /video.*?src="([^"]*\.mp4[^"]*)"/
    ];
    
    for (const pattern of patterns) {
      const match = html.match(pattern);
      if (match && match[1]) {
        let videoUrl = match[1].replace(/\\u0026/g, '&').replace(/\\/g, '');
        
        // Validar que es una URL de video válida
        if (videoUrl && videoUrl.includes('.mp4') && !videoUrl.includes('blob:') && videoUrl.startsWith('http')) {
          console.log('🎬 Video URL encontrada:', videoUrl);
          
          // Buscar thumbnail
          const thumbnailPatterns = [
            /"display_url":"([^"]+)"/,
            /"thumbnail_url":"([^"]+)"/,
            /poster="([^"]+)"/,
            /"image":"([^"]+)"/
          ];
          
          let thumbnail = null;
          for (const thumbPattern of thumbnailPatterns) {
            const thumbMatch = html.match(thumbPattern);
            if (thumbMatch && thumbMatch[1]) {
              thumbnail = thumbMatch[1].replace(/\\/g, '');
              break;
            }
          }
          
          // Buscar título
          const titlePatterns = [
            /"caption":"([^"]+)"/,
            /"title":"([^"]+)"/
          ];
          
          let title = 'Video de Instagram';
          for (const titlePattern of titlePatterns) {
            const titleMatch = html.match(titlePattern);
            if (titleMatch && titleMatch[1]) {
              title = titleMatch[1].replace(/\\/g, '');
              break;
            }
          }
          
          return {
            videoUrl,
            thumbnail,
            title
          };
        }
      }
    }
    
    console.log('❌ No se encontró video en el HTML');
    return null;
  }
  
  // Validar URL de video
  static async validateVideoUrl(url: string): Promise<boolean> {
    try {
      const response = await axios.head(url, {
        timeout: 8000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15'
        },
        maxRedirects: 5
      });
      
      const isValidStatus = response.status >= 200 && response.status < 400;
      const isVideoContent = response.headers['content-type']?.includes('video') || 
                            response.headers['content-type']?.includes('application/octet-stream') ||
                            url.includes('.mp4');
      
      console.log(`✅ Validación: ${url} - Status: ${response.status}, Content-Type: ${response.headers['content-type']}`);
      
      return isValidStatus && isVideoContent;
    } catch (error) {
      console.log(`❌ Error validando: ${url} - ${error.message}`);
      return false;
    }
  }
}
