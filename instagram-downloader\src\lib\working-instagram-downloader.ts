import axios from 'axios';

export interface DownloadResult {
  success: boolean;
  data?: {
    type: 'video';
    qualities: Array<{
      url: string;
      quality: string;
    }>;
    thumbnail?: string;
    caption?: string;
  };
  error?: string;
}

export class WorkingInstagramDownloader {
  
  static async downloadVideo(url: string): Promise<DownloadResult> {
    const cleanUrl = url.split('?')[0];
    
    // Método 1: API de Insta Loader
    const result1 = await this.tryInstaLoader(cleanUrl);
    if (result1.success) return result1;
    
    // Método 2: API de Y2Mate
    const result2 = await this.tryY2Mate(cleanUrl);
    if (result2.success) return result2;
    
    // Método 3: API de SSS Instagram
    const result3 = await this.trySSSInstagram(cleanUrl);
    if (result3.success) return result3;
    
    // Método 4: API de InstaSave
    const result4 = await this.tryInstaSave(cleanUrl);
    if (result4.success) return result4;
    
    return {
      success: false,
      error: 'No se pudo descargar el video. Inténtalo con otra URL.'
    };
  }
  
  private static async tryInstaLoader(url: string): Promise<DownloadResult> {
    try {
      const response = await axios.post('https://instaloader.io/api/post', {
        url: url
      }, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Referer': 'https://instaloader.io/',
          'Origin': 'https://instaloader.io'
        },
        timeout: 20000
      });

      if (response.data && response.data.video_url) {
        return {
          success: true,
          data: {
            type: 'video',
            qualities: [{
              url: response.data.video_url,
              quality: 'HD'
            }],
            thumbnail: response.data.thumbnail_url,
            caption: response.data.caption || ''
          }
        };
      }
      return { success: false };
    } catch (error) {
      return { success: false };
    }
  }
  
  private static async tryY2Mate(url: string): Promise<DownloadResult> {
    try {
      // Paso 1: Analizar la URL
      const analyzeResponse = await axios.post('https://www.y2mate.com/mates/analyzeV2/ajax', 
        new URLSearchParams({
          k_query: url,
          k_page: 'home',
          hl: 'en',
          q_auto: '0'
        }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Referer': 'https://www.y2mate.com/',
          'Origin': 'https://www.y2mate.com'
        },
        timeout: 20000
      });

      if (analyzeResponse.data && analyzeResponse.data.links) {
        const videoLinks = analyzeResponse.data.links.mp4;
        if (videoLinks) {
          const qualities = Object.keys(videoLinks).map(quality => ({
            url: videoLinks[quality].url,
            quality: quality
          }));
          
          return {
            success: true,
            data: {
              type: 'video',
              qualities,
              thumbnail: analyzeResponse.data.thumbnail,
              caption: analyzeResponse.data.title || ''
            }
          };
        }
      }
      return { success: false };
    } catch (error) {
      return { success: false };
    }
  }
  
  private static async trySSSInstagram(url: string): Promise<DownloadResult> {
    try {
      const response = await axios.post('https://sssinstagram.com/api/ig/post', {
        url: url
      }, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15',
          'Referer': 'https://sssinstagram.com/',
          'Origin': 'https://sssinstagram.com'
        },
        timeout: 20000
      });

      if (response.data && response.data.data && response.data.data.length > 0) {
        const videoData = response.data.data[0];
        if (videoData.video_url) {
          return {
            success: true,
            data: {
              type: 'video',
              qualities: [{
                url: videoData.video_url,
                quality: 'HD'
              }],
              thumbnail: videoData.thumbnail_url,
              caption: videoData.caption || ''
            }
          };
        }
      }
      return { success: false };
    } catch (error) {
      return { success: false };
    }
  }
  
  private static async tryInstaSave(url: string): Promise<DownloadResult> {
    try {
      const response = await axios.post('https://instasave.website/system/action.php', 
        new URLSearchParams({
          url: url,
          action: 'post'
        }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
          'Referer': 'https://instasave.website/',
          'Origin': 'https://instasave.website'
        },
        timeout: 20000
      });

      if (response.data) {
        const html = response.data;
        
        // Buscar enlaces de descarga en el HTML
        const videoMatch = html.match(/href="([^"]*\.mp4[^"]*)"/);
        const thumbnailMatch = html.match(/src="([^"]*\.(jpg|jpeg|png)[^"]*)"/);
        
        if (videoMatch && videoMatch[1]) {
          return {
            success: true,
            data: {
              type: 'video',
              qualities: [{
                url: videoMatch[1],
                quality: 'HD'
              }],
              thumbnail: thumbnailMatch ? thumbnailMatch[1] : undefined,
              caption: ''
            }
          };
        }
      }
      return { success: false };
    } catch (error) {
      return { success: false };
    }
  }
  
  // Método adicional usando proxy CORS
  static async tryWithProxy(url: string): Promise<DownloadResult> {
    try {
      const proxyUrl = `https://api.allorigins.win/raw?url=${encodeURIComponent(url)}`;
      
      const response = await axios.get(proxyUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15'
        },
        timeout: 20000
      });

      const html = response.data;
      
      // Buscar patrones de video
      const patterns = [
        /"video_url":"([^"]+)"/,
        /"playback_url":"([^"]+)"/,
        /"src":"([^"]*\.mp4[^"]*)"/,
        /videoUrl":"([^"]+)"/
      ];

      for (const pattern of patterns) {
        const match = html.match(pattern);
        if (match) {
          const videoUrl = match[1].replace(/\\u0026/g, '&').replace(/\\/g, '');
          
          if (videoUrl && videoUrl.includes('.mp4')) {
            const thumbnailMatch = html.match(/"display_url":"([^"]+)"/);
            
            return {
              success: true,
              data: {
                type: 'video',
                qualities: [{
                  url: videoUrl,
                  quality: 'Original'
                }],
                thumbnail: thumbnailMatch ? thumbnailMatch[1].replace(/\\/g, '') : undefined,
                caption: ''
              }
            };
          }
        }
      }
      
      return { success: false };
    } catch (error) {
      return { success: false };
    }
  }
}
