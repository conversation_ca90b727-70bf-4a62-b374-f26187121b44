import axios from 'axios';

export interface VideoResult {
  success: boolean;
  data?: {
    videoUrl: string;
    thumbnail?: string;
    title?: string;
    quality: string;
  };
  error?: string;
}

export class RealInstagramDownloader {
  private static readonly CORS_PROXY = 'https://api.allorigins.win/raw?url=';
  
  static async downloadVideo(url: string): Promise<VideoResult> {
    try {
      // Limpiar la URL
      const cleanUrl = url.split('?')[0];
      
      // Método 1: Usar servicio público de descarga
      const result1 = await this.tryMethod1(cleanUrl);
      if (result1.success) return result1;
      
      // Método 2: Usar otro servicio
      const result2 = await this.tryMethod2(cleanUrl);
      if (result2.success) return result2;
      
      // Método 3: Scraping directo
      const result3 = await this.tryMethod3(cleanUrl);
      if (result3.success) return result3;
      
      return {
        success: false,
        error: 'No se pudo extraer el video. El contenido podría ser privado o no estar disponible.'
      };
      
    } catch (error) {
      return {
        success: false,
        error: 'Error al procesar la solicitud.'
      };
    }
  }
  
  private static async tryMethod1(url: string): Promise<VideoResult> {
    try {
      // Usar API de SnapInsta
      const response = await axios.post('https://snapinsta.app/action.php', 
        new URLSearchParams({
          url: url,
          action: 'post'
        }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Referer': 'https://snapinsta.app/',
        },
        timeout: 15000,
      });

      if (response.data) {
        const html = response.data;
        
        // Buscar enlaces de descarga
        const videoMatch = html.match(/href="([^"]*)" download[^>]*>.*?(Download|Descargar)/i);
        const thumbnailMatch = html.match(/<img[^>]*src="([^"]*)"[^>]*>/);
        
        if (videoMatch && videoMatch[1] && !videoMatch[1].includes('javascript:')) {
          return {
            success: true,
            data: {
              videoUrl: videoMatch[1],
              thumbnail: thumbnailMatch ? thumbnailMatch[1] : undefined,
              quality: 'HD'
            }
          };
        }
      }

      return { success: false, error: 'Method 1 failed' };
    } catch (error) {
      return { success: false, error: 'Method 1 failed' };
    }
  }
  
  private static async tryMethod2(url: string): Promise<VideoResult> {
    try {
      // Usar API de SaveIG
      const response = await axios.post('https://www.saveig.app/api/ajaxSearch', 
        new URLSearchParams({
          q: url,
          t: 'media',
          lang: 'en'
        }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
          'Accept': '*/*',
          'X-Requested-With': 'XMLHttpRequest',
          'Referer': 'https://www.saveig.app/',
        },
        timeout: 15000,
      });

      if (response.data && response.data.data) {
        const html = response.data.data;
        
        // Extraer URL del video
        const videoMatch = html.match(/href="([^"]*\.mp4[^"]*)"/);
        const thumbnailMatch = html.match(/src="([^"]*\.(jpg|jpeg|png)[^"]*)"/);
        
        if (videoMatch && videoMatch[1]) {
          return {
            success: true,
            data: {
              videoUrl: videoMatch[1],
              thumbnail: thumbnailMatch ? thumbnailMatch[1] : undefined,
              quality: 'HD'
            }
          };
        }
      }

      return { success: false, error: 'Method 2 failed' };
    } catch (error) {
      return { success: false, error: 'Method 2 failed' };
    }
  }
  
  private static async tryMethod3(url: string): Promise<VideoResult> {
    try {
      // Scraping directo con proxy CORS
      const proxyUrl = this.CORS_PROXY + encodeURIComponent(url);
      
      const response = await axios.get(proxyUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
        },
        timeout: 15000,
      });

      const html = response.data;
      
      // Buscar patrones de video en el HTML
      const patterns = [
        /"video_url":"([^"]+)"/,
        /"src":"([^"]*\.mp4[^"]*)"/,
        /videoUrl":"([^"]+)"/,
        /"contentUrl":"([^"]+\.mp4[^"]*)"/,
        /video_url\\?":\\?"([^"]+)\\?"/,
      ];

      for (const pattern of patterns) {
        const match = html.match(pattern);
        if (match) {
          const videoUrl = match[1].replace(/\\u0026/g, '&').replace(/\\/g, '');
          
          if (videoUrl && videoUrl.includes('.mp4') && !videoUrl.includes('blob:')) {
            // Buscar thumbnail
            const thumbnailMatch = html.match(/"display_url":"([^"]+)"/);
            const thumbnail = thumbnailMatch ? thumbnailMatch[1].replace(/\\/g, '') : undefined;
            
            return {
              success: true,
              data: {
                videoUrl: videoUrl,
                thumbnail,
                quality: 'Original'
              }
            };
          }
        }
      }

      return { success: false, error: 'Method 3 failed' };
    } catch (error) {
      return { success: false, error: 'Method 3 failed' };
    }
  }
  
  static isValidInstagramUrl(url: string): boolean {
    const regex = /^https?:\/\/(www\.)?(instagram\.com|instagr\.am)\/(p|reel|tv)\/[A-Za-z0-9_-]+/;
    return regex.test(url);
  }
}
