module.exports = {

"[project]/.next-internal/server/app/api/download/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/lib/instagram-service.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "InstagramService": (()=>InstagramService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
;
class InstagramService {
    static USER_AGENTS = [
        'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    ];
    static async getVideoData(url) {
        const shortcode = this.extractShortcode(url);
        if (!shortcode) {
            throw new Error('URL de Instagram no válida');
        }
        // Intentar múltiples métodos de extracción
        const methods = [
            ()=>this.getDataFromThirdPartyAPI(url),
            ()=>this.getDataFromAdvancedScraping(url),
            ()=>this.getDataFromEmbedAPI(shortcode),
            ()=>this.getDataFromHTML(url)
        ];
        for (const method of methods){
            try {
                const result = await method();
                if (result) {
                    return result;
                }
            } catch (error) {
                console.warn('Método falló, intentando siguiente:', error);
                continue;
            }
        }
        throw new Error('No se pudo extraer el video de Instagram. El contenido podría ser privado o no estar disponible.');
    }
    static extractShortcode(url) {
        const match = url.match(/\/(p|reel|tv)\/([A-Za-z0-9_-]+)/);
        return match ? match[2] : null;
    }
    static getRandomUserAgent() {
        return this.USER_AGENTS[Math.floor(Math.random() * this.USER_AGENTS.length)];
    }
    static async getDataFromThirdPartyAPI(url) {
        try {
            // Usar API de terceros para extraer videos de Instagram
            const apiEndpoints = [
                'https://api.saveig.app/api/ajaxSearch',
                'https://v3.saveig.app/api/ajaxSearch'
            ];
            for (const endpoint of apiEndpoints){
                try {
                    const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].post(endpoint, {
                        q: url,
                        t: 'media',
                        lang: 'en'
                    }, {
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'User-Agent': this.getRandomUserAgent(),
                            'Accept': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        timeout: 15000
                    });
                    if (response.data && response.data.data) {
                        const data = response.data.data;
                        // Buscar URLs de video en la respuesta
                        const videoUrlMatch = data.match(/href="([^"]*\.mp4[^"]*)"/);
                        const thumbnailMatch = data.match(/src="([^"]*\.(jpg|jpeg|png)[^"]*)"/);
                        if (videoUrlMatch) {
                            return {
                                type: 'video',
                                qualities: [
                                    {
                                        url: videoUrlMatch[1],
                                        quality: 'HD'
                                    }
                                ],
                                thumbnail: thumbnailMatch ? thumbnailMatch[1] : undefined
                            };
                        }
                    }
                } catch (error) {
                    continue;
                }
            }
            return null;
        } catch (error) {
            throw new Error('Third party API method failed');
        }
    }
    static async getDataFromAdvancedScraping(url) {
        try {
            const userAgent = this.getRandomUserAgent();
            // Método 1: Intentar con diferentes headers
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(url, {
                headers: {
                    'User-Agent': userAgent,
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Cache-Control': 'max-age=0'
                },
                timeout: 15000
            });
            const html = response.data;
            // Buscar múltiples patrones de video URLs
            const patterns = [
                /"video_url":"([^"]+)"/g,
                /"src":"([^"]*\.mp4[^"]*)"/g,
                /videoUrl":"([^"]+)"/g,
                /"contentUrl":"([^"]+\.mp4[^"]*)"/g,
                /video_url\\?":\\?"([^"]+)\\?"/g
            ];
            for (const pattern of patterns){
                const matches = [
                    ...html.matchAll(pattern)
                ];
                if (matches.length > 0) {
                    const videoUrl = matches[0][1].replace(/\\u0026/g, '&').replace(/\\/g, '');
                    if (videoUrl && videoUrl.includes('.mp4')) {
                        // Buscar thumbnail
                        const thumbnailMatch = html.match(/"display_url":"([^"]+)"/);
                        const thumbnail = thumbnailMatch ? thumbnailMatch[1].replace(/\\/g, '') : undefined;
                        return {
                            type: 'video',
                            qualities: [
                                {
                                    url: videoUrl,
                                    quality: 'Original'
                                }
                            ],
                            thumbnail
                        };
                    }
                }
            }
            return null;
        } catch (error) {
            throw new Error('Advanced scraping method failed');
        }
    }
    static async getDataFromHTML(url) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(url, {
                headers: {
                    'User-Agent': this.USER_AGENT,
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                },
                timeout: 10000
            });
            const html = response.data;
            // Método 1: JSON-LD
            const jsonLdMatch = html.match(/<script type="application\/ld\+json"[^>]*>(.*?)<\/script>/s);
            if (jsonLdMatch) {
                try {
                    const jsonData = JSON.parse(jsonLdMatch[1]);
                    if (jsonData.video?.contentUrl) {
                        return {
                            type: 'video',
                            qualities: [
                                {
                                    url: jsonData.video.contentUrl,
                                    quality: 'Original'
                                }
                            ],
                            thumbnail: jsonData.video.thumbnailUrl,
                            caption: jsonData.caption || ''
                        };
                    }
                } catch (e) {
                // Continuar con otros métodos
                }
            }
            // Método 2: window._sharedData
            const sharedDataMatch = html.match(/window\._sharedData\s*=\s*({.+?});/);
            if (sharedDataMatch) {
                try {
                    const sharedData = JSON.parse(sharedDataMatch[1]);
                    const media = sharedData?.entry_data?.PostPage?.[0]?.graphql?.shortcode_media;
                    if (media) {
                        return this.parseGraphQLMedia(media);
                    }
                } catch (e) {
                // Continuar con otros métodos
                }
            }
            // Método 3: Buscar URLs de video directamente en el HTML
            const videoUrlMatch = html.match(/"video_url":"([^"]+)"/);
            if (videoUrlMatch) {
                const videoUrl = videoUrlMatch[1].replace(/\\u0026/g, '&').replace(/\\/g, '');
                return {
                    type: 'video',
                    qualities: [
                        {
                            url: videoUrl,
                            quality: 'Original'
                        }
                    ]
                };
            }
            return null;
        } catch (error) {
            throw new Error('HTML parsing method failed');
        }
    }
    static async getDataFromEmbedAPI(shortcode) {
        try {
            const embedUrl = `https://www.instagram.com/p/${shortcode}/embed/`;
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(embedUrl, {
                headers: {
                    'User-Agent': this.USER_AGENT
                },
                timeout: 10000
            });
            const html = response.data;
            // Buscar datos en el HTML del embed
            const videoMatch = html.match(/"video_url":"([^"]+)"/);
            if (videoMatch) {
                const videoUrl = videoMatch[1].replace(/\\u0026/g, '&').replace(/\\/g, '');
                return {
                    type: 'video',
                    qualities: [
                        {
                            url: videoUrl,
                            quality: 'Original'
                        }
                    ]
                };
            }
            return null;
        } catch (error) {
            throw new Error('Embed API method failed');
        }
    }
    static parseMediaItem(item) {
        if (item.video_versions) {
            // Es un video simple
            const qualities = item.video_versions.map((version)=>({
                    url: version.url,
                    quality: `${version.width}x${version.height}`,
                    width: version.width,
                    height: version.height
                }));
            qualities.sort((a, b)=>b.width * b.height - a.width * a.height);
            return {
                type: 'video',
                qualities,
                thumbnail: item.image_versions2?.candidates?.[0]?.url,
                caption: item.caption?.text || ''
            };
        } else if (item.carousel_media) {
            // Es un carrusel
            const videos = item.carousel_media.filter((media)=>media.video_versions).map((media)=>({
                    qualities: media.video_versions.map((version)=>({
                            url: version.url,
                            quality: `${version.width}x${version.height}`,
                            width: version.width,
                            height: version.height
                        })).sort((a, b)=>b.width * b.height - a.width * a.height),
                    thumbnail: media.image_versions2?.candidates?.[0]?.url
                }));
            if (videos.length > 0) {
                return {
                    type: 'carousel',
                    videos,
                    caption: item.caption?.text || ''
                };
            }
        }
        return null;
    }
    static parseGraphQLMedia(media) {
        if (media.is_video && media.video_url) {
            return {
                type: 'video',
                qualities: [
                    {
                        url: media.video_url,
                        quality: 'Original'
                    }
                ],
                thumbnail: media.display_url,
                caption: media.edge_media_to_caption?.edges?.[0]?.node?.text || ''
            };
        }
        return null;
    }
    static isValidInstagramUrl(url) {
        const instagramRegex = /^https?:\/\/(www\.)?(instagram\.com|instagr\.am)\/(p|reel|tv)\/[A-Za-z0-9_-]+/;
        return instagramRegex.test(url);
    }
}
}}),
"[project]/src/lib/instagram-downloader.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "InstagramDownloader": (()=>InstagramDownloader)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
;
class InstagramDownloader {
    static RAPIDAPI_ENDPOINTS = [
        'https://instagram-downloader-download-instagram-videos-stories.p.rapidapi.com/index',
        'https://instagram-bulk-profile-scrapper.p.rapidapi.com/clients/api/ig/ig_profile'
    ];
    static PUBLIC_APIS = [
        'https://api.downloadgram.com/media',
        'https://www.saveig.app/api/ajaxSearch',
        'https://snapinsta.app/action.php'
    ];
    static async downloadInstagramVideo(url) {
        // Limpiar la URL
        const cleanUrl = this.cleanInstagramUrl(url);
        if (!this.isValidInstagramUrl(cleanUrl)) {
            return {
                success: false,
                error: 'URL de Instagram no válida'
            };
        }
        // Intentar diferentes métodos
        const methods = [
            ()=>this.tryPublicAPI1(cleanUrl),
            ()=>this.tryPublicAPI2(cleanUrl),
            ()=>this.tryPublicAPI3(cleanUrl),
            ()=>this.tryDirectScraping(cleanUrl)
        ];
        for (const method of methods){
            try {
                const result = await method();
                if (result.success) {
                    return result;
                }
            } catch (error) {
                console.warn('Método falló:', error);
                continue;
            }
        }
        return {
            success: false,
            error: 'No se pudo descargar el video. El contenido podría ser privado o no estar disponible.'
        };
    }
    static cleanInstagramUrl(url) {
        // Remover parámetros de tracking
        return url.split('?')[0];
    }
    static isValidInstagramUrl(url) {
        const regex = /^https?:\/\/(www\.)?(instagram\.com|instagr\.am)\/(p|reel|tv)\/[A-Za-z0-9_-]+/;
        return regex.test(url);
    }
    static async tryPublicAPI1(url) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].post('https://www.saveig.app/api/ajaxSearch', new URLSearchParams({
                q: url,
                t: 'media',
                lang: 'en'
            }), {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept': '*/*',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                timeout: 15000
            });
            if (response.data && response.data.data) {
                const html = response.data.data;
                // Extraer URL del video
                const videoMatch = html.match(/href="([^"]*\.mp4[^"]*)"/);
                const thumbnailMatch = html.match(/src="([^"]*\.(jpg|jpeg|png)[^"]*)"/);
                if (videoMatch) {
                    return {
                        success: true,
                        data: {
                            url: videoMatch[1],
                            thumbnail: thumbnailMatch ? thumbnailMatch[1] : undefined,
                            quality: 'HD'
                        }
                    };
                }
            }
            return {
                success: false,
                error: 'No video found in API response'
            };
        } catch (error) {
            throw new Error('Public API 1 failed');
        }
    }
    static async tryPublicAPI2(url) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].post('https://snapinsta.app/action.php', new URLSearchParams({
                url: url,
                action: 'post'
            }), {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                },
                timeout: 15000
            });
            if (response.data) {
                const html = response.data;
                // Buscar enlaces de descarga
                const videoMatch = html.match(/href="([^"]*)" download[^>]*>.*?Download/i);
                const thumbnailMatch = html.match(/<img[^>]*src="([^"]*)"[^>]*>/);
                if (videoMatch) {
                    return {
                        success: true,
                        data: {
                            url: videoMatch[1],
                            thumbnail: thumbnailMatch ? thumbnailMatch[1] : undefined,
                            quality: 'Original'
                        }
                    };
                }
            }
            return {
                success: false,
                error: 'No video found in API response'
            };
        } catch (error) {
            throw new Error('Public API 2 failed');
        }
    }
    static async tryPublicAPI3(url) {
        try {
            // Usar una API alternativa
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`https://api.downloadgram.com/media?url=${encodeURIComponent(url)}`, {
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                    'Accept': 'application/json'
                },
                timeout: 15000
            });
            if (response.data && response.data.video_url) {
                return {
                    success: true,
                    data: {
                        url: response.data.video_url,
                        thumbnail: response.data.thumbnail_url,
                        title: response.data.title,
                        quality: response.data.quality || 'HD'
                    }
                };
            }
            return {
                success: false,
                error: 'No video found in API response'
            };
        } catch (error) {
            throw new Error('Public API 3 failed');
        }
    }
    static async tryDirectScraping(url) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(url, {
                headers: {
                    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1'
                },
                timeout: 15000
            });
            const html = response.data;
            // Buscar patrones de video en el HTML
            const patterns = [
                /"video_url":"([^"]+)"/,
                /"src":"([^"]*\.mp4[^"]*)"/,
                /videoUrl":"([^"]+)"/,
                /"contentUrl":"([^"]+\.mp4[^"]*)"/
            ];
            for (const pattern of patterns){
                const match = html.match(pattern);
                if (match) {
                    const videoUrl = match[1].replace(/\\u0026/g, '&').replace(/\\/g, '');
                    if (videoUrl && videoUrl.includes('.mp4')) {
                        // Buscar thumbnail
                        const thumbnailMatch = html.match(/"display_url":"([^"]+)"/);
                        const thumbnail = thumbnailMatch ? thumbnailMatch[1].replace(/\\/g, '') : undefined;
                        return {
                            success: true,
                            data: {
                                url: videoUrl,
                                thumbnail,
                                quality: 'Original'
                            }
                        };
                    }
                }
            }
            return {
                success: false,
                error: 'No video found in HTML'
            };
        } catch (error) {
            throw new Error('Direct scraping failed');
        }
    }
}
}}),
"[project]/src/app/api/download/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$instagram$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/instagram-service.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$instagram$2d$downloader$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/instagram-downloader.ts [app-route] (ecmascript)");
;
;
;
async function POST(request) {
    try {
        const { url } = await request.json();
        if (!url) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'URL es requerida'
            }, {
                status: 400
            });
        }
        // Validar que sea una URL de Instagram
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$instagram$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["InstagramService"].isValidInstagramUrl(url)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'URL de Instagram no válida'
            }, {
                status: 400
            });
        }
        try {
            // Intentar primero con el nuevo downloader
            const downloadResult = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$instagram$2d$downloader$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["InstagramDownloader"].downloadInstagramVideo(url);
            if (downloadResult.success && downloadResult.data) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    type: 'video',
                    qualities: [
                        {
                            url: downloadResult.data.url,
                            quality: downloadResult.data.quality || 'HD'
                        }
                    ],
                    thumbnail: downloadResult.data.thumbnail,
                    caption: downloadResult.data.title || ''
                });
            }
            // Si falla, intentar con el servicio original
            const videoData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$instagram$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["InstagramService"].getVideoData(url);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(videoData);
        } catch (error) {
            console.error('Error al obtener datos de Instagram:', error.message);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: error.message || 'No se pudo acceder al contenido de Instagram. El video podría ser privado o no estar disponible.'
            }, {
                status: 503
            });
        }
    } catch (error) {
        console.error('Error en la API:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Error interno del servidor'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__a790e1b1._.js.map