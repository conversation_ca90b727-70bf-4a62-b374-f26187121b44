{"name": "cheerio", "version": "1.1.0", "description": "The fast, flexible & elegant library for parsing and manipulating HTML and XML.", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "dom", "xml", "html"], "homepage": "https://cheerio.js.org/", "bugs": {"url": "https://github.com/cheeriojs/cheerio/issues"}, "repository": {"type": "git", "url": "git://github.com/cheeriojs/cheerio.git"}, "funding": "https://github.com/cheeriojs/cheerio?sponsor=1", "license": "MIT", "author": "<PERSON> <matt<PERSON><PERSON>@gmail.com>", "maintainers": ["<PERSON> <<EMAIL>>"], "type": "module", "exports": {".": {"browser": {"types": "./dist/browser/index.d.ts", "default": "./dist/browser/index.js"}, "import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./slim": {"browser": {"types": "./dist/browser/slim.d.ts", "default": "./dist/browser/slim.js"}, "import": {"types": "./dist/esm/slim.d.ts", "default": "./dist/esm/slim.js"}, "require": {"types": "./dist/commonjs/slim.d.ts", "default": "./dist/commonjs/slim.js"}}, "./utils": {"browser": {"types": "./dist/browser/utils.d.ts", "default": "./dist/browser/utils.js"}, "import": {"types": "./dist/esm/utils.d.ts", "default": "./dist/esm/utils.js"}, "require": {"types": "./dist/commonjs/utils.d.ts", "default": "./dist/commonjs/utils.js"}}, "./package.json": "./package.json"}, "main": "./dist/commonjs/index.js", "module": "./dist/esm/index.js", "browser": "./dist/browser/index.js", "types": "./dist/commonjs/index.d.ts", "files": ["dist", "src"], "scripts": {"benchmark": "node --import=tsx benchmark/benchmark.ts", "build": "tshy", "format": "npm run format:es && npm run format:prettier", "format:es": "eslint . --fix", "format:prettier": "npm run format:prettier:raw -- --write", "format:prettier:raw": "prettier \"**/*.{{m,c,}{j,t}s{x,},md{x,},json,y{a,}ml}\" --ignore-path .gitignore", "lint": "npm run lint:es && npm run lint:prettier && npm run lint:ts", "lint:es": "eslint .", "lint:prettier": "npm run format:prettier:raw -- --check", "lint:ts": "tsc --noEmit", "prepare": "husky", "prepublishOnly": "npm run build", "test": "npm run lint && npm run test:vi", "test:vi": "vitest run", "update-sponsors": "tsx scripts/fetch-sponsors.mts"}, "lint-staged": {"*.js": ["prettier --write", "eslint --fix"], "*.{json,md,ts,yml}": ["prettier --write"]}, "prettier": {"plugins": ["./node_modules/prettier-plugin-jsdoc/dist/index.js"], "proseWrap": "always", "singleQuote": true, "tabWidth": 2, "tsdoc": true}, "dependencies": {"cheerio-select": "^2.1.0", "dom-serializer": "^2.0.0", "domhandler": "^5.0.3", "domutils": "^3.2.2", "encoding-sniffer": "^0.2.0", "htmlparser2": "^10.0.0", "parse5": "^7.3.0", "parse5-htmlparser2-tree-adapter": "^7.1.0", "parse5-parser-stream": "^7.1.2", "undici": "^7.10.0", "whatwg-mimetype": "^4.0.0"}, "devDependencies": {"@eslint/compat": "^1.2.9", "@eslint/js": "^9.28.0", "@imgix/js-core": "^3.8.0", "@octokit/graphql": "^9.0.1", "@types/jsdom": "^21.1.7", "@types/node": "^22.15.30", "@types/whatwg-mimetype": "^3.0.2", "@vitest/coverage-v8": "^3.2.2", "@vitest/eslint-plugin": "^1.2.1", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jsdoc": "^50.7.1", "eslint-plugin-n": "^17.19.0", "eslint-plugin-unicorn": "^59.0.1", "globals": "^16.2.0", "husky": "^9.1.7", "jquery": "^3.7.1", "jsdom": "^26.1.0", "lint-staged": "^16.1.0", "prettier": "^3.5.3", "prettier-plugin-jsdoc": "^1.3.2", "tinybench": "^4.0.1", "tshy": "^3.0.2", "tsx": "^4.19.4", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1", "vitest": "^3.1.4"}, "engines": {"node": ">=18.17"}, "tshy": {"esmDialects": ["browser"], "exports": {".": "./src/index.ts", "./slim": "./src/slim.ts", "./utils": "./src/utils.ts", "./package.json": "./package.json"}, "exclude": ["**/*.spec.ts", "**/__fixtures__/*", "**/__tests__/*", "**/__snapshots__/*"]}}