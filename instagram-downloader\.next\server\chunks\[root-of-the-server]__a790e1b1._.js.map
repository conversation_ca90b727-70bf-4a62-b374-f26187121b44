{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/videostiktok/instagram-downloader/src/lib/instagram-service.ts"], "sourcesContent": ["import axios from 'axios';\n\nexport interface VideoQuality {\n  url: string;\n  quality: string;\n  width?: number;\n  height?: number;\n  size?: string;\n}\n\nexport interface VideoData {\n  type: 'video' | 'carousel';\n  qualities?: VideoQuality[];\n  videos?: { qualities: VideoQuality[]; thumbnail?: string }[];\n  thumbnail?: string;\n  caption?: string;\n}\n\nexport class InstagramService {\n  private static readonly USER_AGENTS = [\n    'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',\n    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',\n    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',\n    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'\n  ];\n\n  static async getVideoData(url: string): Promise<VideoData> {\n    const shortcode = this.extractShortcode(url);\n    if (!shortcode) {\n      throw new Error('URL de Instagram no válida');\n    }\n\n    // Intentar múltiples métodos de extracción\n    const methods = [\n      () => this.getDataFromThirdPartyAPI(url),\n      () => this.getDataFromAdvancedScraping(url),\n      () => this.getDataFromEmbedAPI(shortcode),\n      () => this.getDataFromHTML(url),\n    ];\n\n    for (const method of methods) {\n      try {\n        const result = await method();\n        if (result) {\n          return result;\n        }\n      } catch (error) {\n        console.warn('Método falló, intentando siguiente:', error);\n        continue;\n      }\n    }\n\n    throw new Error('No se pudo extraer el video de Instagram. El contenido podría ser privado o no estar disponible.');\n  }\n\n  private static extractShortcode(url: string): string | null {\n    const match = url.match(/\\/(p|reel|tv)\\/([A-Za-z0-9_-]+)/);\n    return match ? match[2] : null;\n  }\n\n  private static getRandomUserAgent(): string {\n    return this.USER_AGENTS[Math.floor(Math.random() * this.USER_AGENTS.length)];\n  }\n\n  private static async getDataFromThirdPartyAPI(url: string): Promise<VideoData | null> {\n    try {\n      // Usar API de terceros para extraer videos de Instagram\n      const apiEndpoints = [\n        'https://api.saveig.app/api/ajaxSearch',\n        'https://v3.saveig.app/api/ajaxSearch'\n      ];\n\n      for (const endpoint of apiEndpoints) {\n        try {\n          const response = await axios.post(endpoint, {\n            q: url,\n            t: 'media',\n            lang: 'en'\n          }, {\n            headers: {\n              'Content-Type': 'application/x-www-form-urlencoded',\n              'User-Agent': this.getRandomUserAgent(),\n              'Accept': 'application/json',\n              'X-Requested-With': 'XMLHttpRequest',\n            },\n            timeout: 15000,\n          });\n\n          if (response.data && response.data.data) {\n            const data = response.data.data;\n\n            // Buscar URLs de video en la respuesta\n            const videoUrlMatch = data.match(/href=\"([^\"]*\\.mp4[^\"]*)\"/);\n            const thumbnailMatch = data.match(/src=\"([^\"]*\\.(jpg|jpeg|png)[^\"]*)\"/);\n\n            if (videoUrlMatch) {\n              return {\n                type: 'video',\n                qualities: [{\n                  url: videoUrlMatch[1],\n                  quality: 'HD',\n                }],\n                thumbnail: thumbnailMatch ? thumbnailMatch[1] : undefined,\n              };\n            }\n          }\n        } catch (error) {\n          continue;\n        }\n      }\n\n      return null;\n    } catch (error) {\n      throw new Error('Third party API method failed');\n    }\n  }\n\n  private static async getDataFromAdvancedScraping(url: string): Promise<VideoData | null> {\n    try {\n      const userAgent = this.getRandomUserAgent();\n\n      // Método 1: Intentar con diferentes headers\n      const response = await axios.get(url, {\n        headers: {\n          'User-Agent': userAgent,\n          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',\n          'Accept-Language': 'en-US,en;q=0.5',\n          'Accept-Encoding': 'gzip, deflate, br',\n          'Connection': 'keep-alive',\n          'Upgrade-Insecure-Requests': '1',\n          'Sec-Fetch-Dest': 'document',\n          'Sec-Fetch-Mode': 'navigate',\n          'Sec-Fetch-Site': 'none',\n          'Cache-Control': 'max-age=0',\n        },\n        timeout: 15000,\n      });\n\n      const html = response.data;\n\n      // Buscar múltiples patrones de video URLs\n      const patterns = [\n        /\"video_url\":\"([^\"]+)\"/g,\n        /\"src\":\"([^\"]*\\.mp4[^\"]*)\"/g,\n        /videoUrl\":\"([^\"]+)\"/g,\n        /\"contentUrl\":\"([^\"]+\\.mp4[^\"]*)\"/g,\n        /video_url\\\\?\":\\\\?\"([^\"]+)\\\\?\"/g,\n      ];\n\n      for (const pattern of patterns) {\n        const matches = [...html.matchAll(pattern)];\n        if (matches.length > 0) {\n          const videoUrl = matches[0][1].replace(/\\\\u0026/g, '&').replace(/\\\\/g, '');\n\n          if (videoUrl && videoUrl.includes('.mp4')) {\n            // Buscar thumbnail\n            const thumbnailMatch = html.match(/\"display_url\":\"([^\"]+)\"/);\n            const thumbnail = thumbnailMatch ? thumbnailMatch[1].replace(/\\\\/g, '') : undefined;\n\n            return {\n              type: 'video',\n              qualities: [{\n                url: videoUrl,\n                quality: 'Original',\n              }],\n              thumbnail,\n            };\n          }\n        }\n      }\n\n      return null;\n    } catch (error) {\n      throw new Error('Advanced scraping method failed');\n    }\n  }\n\n  private static async getDataFromHTML(url: string): Promise<VideoData | null> {\n    try {\n      const response = await axios.get(url, {\n        headers: {\n          'User-Agent': this.USER_AGENT,\n          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',\n        },\n        timeout: 10000,\n      });\n\n      const html = response.data;\n      \n      // Método 1: JSON-LD\n      const jsonLdMatch = html.match(/<script type=\"application\\/ld\\+json\"[^>]*>(.*?)<\\/script>/s);\n      if (jsonLdMatch) {\n        try {\n          const jsonData = JSON.parse(jsonLdMatch[1]);\n          if (jsonData.video?.contentUrl) {\n            return {\n              type: 'video',\n              qualities: [{\n                url: jsonData.video.contentUrl,\n                quality: 'Original',\n              }],\n              thumbnail: jsonData.video.thumbnailUrl,\n              caption: jsonData.caption || '',\n            };\n          }\n        } catch (e) {\n          // Continuar con otros métodos\n        }\n      }\n\n      // Método 2: window._sharedData\n      const sharedDataMatch = html.match(/window\\._sharedData\\s*=\\s*({.+?});/);\n      if (sharedDataMatch) {\n        try {\n          const sharedData = JSON.parse(sharedDataMatch[1]);\n          const media = sharedData?.entry_data?.PostPage?.[0]?.graphql?.shortcode_media;\n          \n          if (media) {\n            return this.parseGraphQLMedia(media);\n          }\n        } catch (e) {\n          // Continuar con otros métodos\n        }\n      }\n\n      // Método 3: Buscar URLs de video directamente en el HTML\n      const videoUrlMatch = html.match(/\"video_url\":\"([^\"]+)\"/);\n      if (videoUrlMatch) {\n        const videoUrl = videoUrlMatch[1].replace(/\\\\u0026/g, '&').replace(/\\\\/g, '');\n        return {\n          type: 'video',\n          qualities: [{\n            url: videoUrl,\n            quality: 'Original',\n          }],\n        };\n      }\n\n      return null;\n    } catch (error) {\n      throw new Error('HTML parsing method failed');\n    }\n  }\n\n  private static async getDataFromEmbedAPI(shortcode: string): Promise<VideoData | null> {\n    try {\n      const embedUrl = `https://www.instagram.com/p/${shortcode}/embed/`;\n      const response = await axios.get(embedUrl, {\n        headers: {\n          'User-Agent': this.USER_AGENT,\n        },\n        timeout: 10000,\n      });\n\n      const html = response.data;\n      \n      // Buscar datos en el HTML del embed\n      const videoMatch = html.match(/\"video_url\":\"([^\"]+)\"/);\n      if (videoMatch) {\n        const videoUrl = videoMatch[1].replace(/\\\\u0026/g, '&').replace(/\\\\/g, '');\n        return {\n          type: 'video',\n          qualities: [{\n            url: videoUrl,\n            quality: 'Original',\n          }],\n        };\n      }\n\n      return null;\n    } catch (error) {\n      throw new Error('Embed API method failed');\n    }\n  }\n\n  private static parseMediaItem(item: any): VideoData | null {\n    if (item.video_versions) {\n      // Es un video simple\n      const qualities = item.video_versions.map((version: any) => ({\n        url: version.url,\n        quality: `${version.width}x${version.height}`,\n        width: version.width,\n        height: version.height,\n      }));\n\n      qualities.sort((a: any, b: any) => (b.width * b.height) - (a.width * a.height));\n\n      return {\n        type: 'video',\n        qualities,\n        thumbnail: item.image_versions2?.candidates?.[0]?.url,\n        caption: item.caption?.text || '',\n      };\n    } else if (item.carousel_media) {\n      // Es un carrusel\n      const videos = item.carousel_media\n        .filter((media: any) => media.video_versions)\n        .map((media: any) => ({\n          qualities: media.video_versions.map((version: any) => ({\n            url: version.url,\n            quality: `${version.width}x${version.height}`,\n            width: version.width,\n            height: version.height,\n          })).sort((a: any, b: any) => (b.width * b.height) - (a.width * a.height)),\n          thumbnail: media.image_versions2?.candidates?.[0]?.url,\n        }));\n\n      if (videos.length > 0) {\n        return {\n          type: 'carousel',\n          videos,\n          caption: item.caption?.text || '',\n        };\n      }\n    }\n\n    return null;\n  }\n\n  private static parseGraphQLMedia(media: any): VideoData | null {\n    if (media.is_video && media.video_url) {\n      return {\n        type: 'video',\n        qualities: [{\n          url: media.video_url,\n          quality: 'Original',\n        }],\n        thumbnail: media.display_url,\n        caption: media.edge_media_to_caption?.edges?.[0]?.node?.text || '',\n      };\n    }\n\n    return null;\n  }\n\n  static isValidInstagramUrl(url: string): boolean {\n    const instagramRegex = /^https?:\\/\\/(www\\.)?(instagram\\.com|instagr\\.am)\\/(p|reel|tv)\\/[A-Za-z0-9_-]+/;\n    return instagramRegex.test(url);\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAkBO,MAAM;IACX,OAAwB,cAAc;QACpC;QACA;QACA;QACA;KACD,CAAC;IAEF,aAAa,aAAa,GAAW,EAAsB;QACzD,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;QACxC,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,2CAA2C;QAC3C,MAAM,UAAU;YACd,IAAM,IAAI,CAAC,wBAAwB,CAAC;YACpC,IAAM,IAAI,CAAC,2BAA2B,CAAC;YACvC,IAAM,IAAI,CAAC,mBAAmB,CAAC;YAC/B,IAAM,IAAI,CAAC,eAAe,CAAC;SAC5B;QAED,KAAK,MAAM,UAAU,QAAS;YAC5B,IAAI;gBACF,MAAM,SAAS,MAAM;gBACrB,IAAI,QAAQ;oBACV,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,uCAAuC;gBACpD;YACF;QACF;QAEA,MAAM,IAAI,MAAM;IAClB;IAEA,OAAe,iBAAiB,GAAW,EAAiB;QAC1D,MAAM,QAAQ,IAAI,KAAK,CAAC;QACxB,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;IAC5B;IAEA,OAAe,qBAA6B;QAC1C,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;IAC9E;IAEA,aAAqB,yBAAyB,GAAW,EAA6B;QACpF,IAAI;YACF,wDAAwD;YACxD,MAAM,eAAe;gBACnB;gBACA;aACD;YAED,KAAK,MAAM,YAAY,aAAc;gBACnC,IAAI;oBACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,UAAU;wBAC1C,GAAG;wBACH,GAAG;wBACH,MAAM;oBACR,GAAG;wBACD,SAAS;4BACP,gBAAgB;4BAChB,cAAc,IAAI,CAAC,kBAAkB;4BACrC,UAAU;4BACV,oBAAoB;wBACtB;wBACA,SAAS;oBACX;oBAEA,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;wBACvC,MAAM,OAAO,SAAS,IAAI,CAAC,IAAI;wBAE/B,uCAAuC;wBACvC,MAAM,gBAAgB,KAAK,KAAK,CAAC;wBACjC,MAAM,iBAAiB,KAAK,KAAK,CAAC;wBAElC,IAAI,eAAe;4BACjB,OAAO;gCACL,MAAM;gCACN,WAAW;oCAAC;wCACV,KAAK,aAAa,CAAC,EAAE;wCACrB,SAAS;oCACX;iCAAE;gCACF,WAAW,iBAAiB,cAAc,CAAC,EAAE,GAAG;4BAClD;wBACF;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd;gBACF;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAqB,4BAA4B,GAAW,EAA6B;QACvF,IAAI;YACF,MAAM,YAAY,IAAI,CAAC,kBAAkB;YAEzC,4CAA4C;YAC5C,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;gBACpC,SAAS;oBACP,cAAc;oBACd,UAAU;oBACV,mBAAmB;oBACnB,mBAAmB;oBACnB,cAAc;oBACd,6BAA6B;oBAC7B,kBAAkB;oBAClB,kBAAkB;oBAClB,kBAAkB;oBAClB,iBAAiB;gBACnB;gBACA,SAAS;YACX;YAEA,MAAM,OAAO,SAAS,IAAI;YAE1B,0CAA0C;YAC1C,MAAM,WAAW;gBACf;gBACA;gBACA;gBACA;gBACA;aACD;YAED,KAAK,MAAM,WAAW,SAAU;gBAC9B,MAAM,UAAU;uBAAI,KAAK,QAAQ,CAAC;iBAAS;gBAC3C,IAAI,QAAQ,MAAM,GAAG,GAAG;oBACtB,MAAM,WAAW,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,YAAY,KAAK,OAAO,CAAC,OAAO;oBAEvE,IAAI,YAAY,SAAS,QAAQ,CAAC,SAAS;wBACzC,mBAAmB;wBACnB,MAAM,iBAAiB,KAAK,KAAK,CAAC;wBAClC,MAAM,YAAY,iBAAiB,cAAc,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,MAAM;wBAE1E,OAAO;4BACL,MAAM;4BACN,WAAW;gCAAC;oCACV,KAAK;oCACL,SAAS;gCACX;6BAAE;4BACF;wBACF;oBACF;gBACF;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAqB,gBAAgB,GAAW,EAA6B;QAC3E,IAAI;YACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;gBACpC,SAAS;oBACP,cAAc,IAAI,CAAC,UAAU;oBAC7B,UAAU;gBACZ;gBACA,SAAS;YACX;YAEA,MAAM,OAAO,SAAS,IAAI;YAE1B,oBAAoB;YACpB,MAAM,cAAc,KAAK,KAAK,CAAC;YAC/B,IAAI,aAAa;gBACf,IAAI;oBACF,MAAM,WAAW,KAAK,KAAK,CAAC,WAAW,CAAC,EAAE;oBAC1C,IAAI,SAAS,KAAK,EAAE,YAAY;wBAC9B,OAAO;4BACL,MAAM;4BACN,WAAW;gCAAC;oCACV,KAAK,SAAS,KAAK,CAAC,UAAU;oCAC9B,SAAS;gCACX;6BAAE;4BACF,WAAW,SAAS,KAAK,CAAC,YAAY;4BACtC,SAAS,SAAS,OAAO,IAAI;wBAC/B;oBACF;gBACF,EAAE,OAAO,GAAG;gBACV,8BAA8B;gBAChC;YACF;YAEA,+BAA+B;YAC/B,MAAM,kBAAkB,KAAK,KAAK,CAAC;YACnC,IAAI,iBAAiB;gBACnB,IAAI;oBACF,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,CAAC,EAAE;oBAChD,MAAM,QAAQ,YAAY,YAAY,UAAU,CAAC,EAAE,EAAE,SAAS;oBAE9D,IAAI,OAAO;wBACT,OAAO,IAAI,CAAC,iBAAiB,CAAC;oBAChC;gBACF,EAAE,OAAO,GAAG;gBACV,8BAA8B;gBAChC;YACF;YAEA,yDAAyD;YACzD,MAAM,gBAAgB,KAAK,KAAK,CAAC;YACjC,IAAI,eAAe;gBACjB,MAAM,WAAW,aAAa,CAAC,EAAE,CAAC,OAAO,CAAC,YAAY,KAAK,OAAO,CAAC,OAAO;gBAC1E,OAAO;oBACL,MAAM;oBACN,WAAW;wBAAC;4BACV,KAAK;4BACL,SAAS;wBACX;qBAAE;gBACJ;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAqB,oBAAoB,SAAiB,EAA6B;QACrF,IAAI;YACF,MAAM,WAAW,CAAC,4BAA4B,EAAE,UAAU,OAAO,CAAC;YAClE,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,UAAU;gBACzC,SAAS;oBACP,cAAc,IAAI,CAAC,UAAU;gBAC/B;gBACA,SAAS;YACX;YAEA,MAAM,OAAO,SAAS,IAAI;YAE1B,oCAAoC;YACpC,MAAM,aAAa,KAAK,KAAK,CAAC;YAC9B,IAAI,YAAY;gBACd,MAAM,WAAW,UAAU,CAAC,EAAE,CAAC,OAAO,CAAC,YAAY,KAAK,OAAO,CAAC,OAAO;gBACvE,OAAO;oBACL,MAAM;oBACN,WAAW;wBAAC;4BACV,KAAK;4BACL,SAAS;wBACX;qBAAE;gBACJ;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,OAAe,eAAe,IAAS,EAAoB;QACzD,IAAI,KAAK,cAAc,EAAE;YACvB,qBAAqB;YACrB,MAAM,YAAY,KAAK,cAAc,CAAC,GAAG,CAAC,CAAC,UAAiB,CAAC;oBAC3D,KAAK,QAAQ,GAAG;oBAChB,SAAS,GAAG,QAAQ,KAAK,CAAC,CAAC,EAAE,QAAQ,MAAM,EAAE;oBAC7C,OAAO,QAAQ,KAAK;oBACpB,QAAQ,QAAQ,MAAM;gBACxB,CAAC;YAED,UAAU,IAAI,CAAC,CAAC,GAAQ,IAAW,AAAC,EAAE,KAAK,GAAG,EAAE,MAAM,GAAK,EAAE,KAAK,GAAG,EAAE,MAAM;YAE7E,OAAO;gBACL,MAAM;gBACN;gBACA,WAAW,KAAK,eAAe,EAAE,YAAY,CAAC,EAAE,EAAE;gBAClD,SAAS,KAAK,OAAO,EAAE,QAAQ;YACjC;QACF,OAAO,IAAI,KAAK,cAAc,EAAE;YAC9B,iBAAiB;YACjB,MAAM,SAAS,KAAK,cAAc,CAC/B,MAAM,CAAC,CAAC,QAAe,MAAM,cAAc,EAC3C,GAAG,CAAC,CAAC,QAAe,CAAC;oBACpB,WAAW,MAAM,cAAc,CAAC,GAAG,CAAC,CAAC,UAAiB,CAAC;4BACrD,KAAK,QAAQ,GAAG;4BAChB,SAAS,GAAG,QAAQ,KAAK,CAAC,CAAC,EAAE,QAAQ,MAAM,EAAE;4BAC7C,OAAO,QAAQ,KAAK;4BACpB,QAAQ,QAAQ,MAAM;wBACxB,CAAC,GAAG,IAAI,CAAC,CAAC,GAAQ,IAAW,AAAC,EAAE,KAAK,GAAG,EAAE,MAAM,GAAK,EAAE,KAAK,GAAG,EAAE,MAAM;oBACvE,WAAW,MAAM,eAAe,EAAE,YAAY,CAAC,EAAE,EAAE;gBACrD,CAAC;YAEH,IAAI,OAAO,MAAM,GAAG,GAAG;gBACrB,OAAO;oBACL,MAAM;oBACN;oBACA,SAAS,KAAK,OAAO,EAAE,QAAQ;gBACjC;YACF;QACF;QAEA,OAAO;IACT;IAEA,OAAe,kBAAkB,KAAU,EAAoB;QAC7D,IAAI,MAAM,QAAQ,IAAI,MAAM,SAAS,EAAE;YACrC,OAAO;gBACL,MAAM;gBACN,WAAW;oBAAC;wBACV,KAAK,MAAM,SAAS;wBACpB,SAAS;oBACX;iBAAE;gBACF,WAAW,MAAM,WAAW;gBAC5B,SAAS,MAAM,qBAAqB,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,QAAQ;YAClE;QACF;QAEA,OAAO;IACT;IAEA,OAAO,oBAAoB,GAAW,EAAW;QAC/C,MAAM,iBAAiB;QACvB,OAAO,eAAe,IAAI,CAAC;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/videostiktok/instagram-downloader/src/lib/instagram-downloader.ts"], "sourcesContent": ["import axios from 'axios';\n\nexport interface DownloadResult {\n  success: boolean;\n  data?: {\n    url: string;\n    thumbnail?: string;\n    title?: string;\n    quality?: string;\n  };\n  error?: string;\n}\n\nexport class InstagramDownloader {\n  private static readonly RAPIDAPI_ENDPOINTS = [\n    'https://instagram-downloader-download-instagram-videos-stories.p.rapidapi.com/index',\n    'https://instagram-bulk-profile-scrapper.p.rapidapi.com/clients/api/ig/ig_profile',\n  ];\n\n  private static readonly PUBLIC_APIS = [\n    'https://api.downloadgram.com/media',\n    'https://www.saveig.app/api/ajaxSearch',\n    'https://snapinsta.app/action.php',\n  ];\n\n  static async downloadInstagramVideo(url: string): Promise<DownloadResult> {\n    // Limpiar la URL\n    const cleanUrl = this.cleanInstagramUrl(url);\n    \n    if (!this.isValidInstagramUrl(cleanUrl)) {\n      return {\n        success: false,\n        error: 'URL de Instagram no válida'\n      };\n    }\n\n    // Intentar diferentes métodos\n    const methods = [\n      () => this.tryPublicAPI1(cleanUrl),\n      () => this.tryPublicAPI2(cleanUrl),\n      () => this.tryPublicAPI3(cleanUrl),\n      () => this.tryDirectScraping(cleanUrl),\n    ];\n\n    for (const method of methods) {\n      try {\n        const result = await method();\n        if (result.success) {\n          return result;\n        }\n      } catch (error) {\n        console.warn('Método falló:', error);\n        continue;\n      }\n    }\n\n    return {\n      success: false,\n      error: 'No se pudo descargar el video. El contenido podría ser privado o no estar disponible.'\n    };\n  }\n\n  private static cleanInstagramUrl(url: string): string {\n    // Remover parámetros de tracking\n    return url.split('?')[0];\n  }\n\n  private static isValidInstagramUrl(url: string): boolean {\n    const regex = /^https?:\\/\\/(www\\.)?(instagram\\.com|instagr\\.am)\\/(p|reel|tv)\\/[A-Za-z0-9_-]+/;\n    return regex.test(url);\n  }\n\n  private static async tryPublicAPI1(url: string): Promise<DownloadResult> {\n    try {\n      const response = await axios.post('https://www.saveig.app/api/ajaxSearch', \n        new URLSearchParams({\n          q: url,\n          t: 'media',\n          lang: 'en'\n        }), {\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded',\n          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n          'Accept': '*/*',\n          'X-Requested-With': 'XMLHttpRequest',\n        },\n        timeout: 15000,\n      });\n\n      if (response.data && response.data.data) {\n        const html = response.data.data;\n        \n        // Extraer URL del video\n        const videoMatch = html.match(/href=\"([^\"]*\\.mp4[^\"]*)\"/);\n        const thumbnailMatch = html.match(/src=\"([^\"]*\\.(jpg|jpeg|png)[^\"]*)\"/);\n        \n        if (videoMatch) {\n          return {\n            success: true,\n            data: {\n              url: videoMatch[1],\n              thumbnail: thumbnailMatch ? thumbnailMatch[1] : undefined,\n              quality: 'HD'\n            }\n          };\n        }\n      }\n\n      return { success: false, error: 'No video found in API response' };\n    } catch (error) {\n      throw new Error('Public API 1 failed');\n    }\n  }\n\n  private static async tryPublicAPI2(url: string): Promise<DownloadResult> {\n    try {\n      const response = await axios.post('https://snapinsta.app/action.php', \n        new URLSearchParams({\n          url: url,\n          action: 'post'\n        }), {\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded',\n          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15',\n          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',\n        },\n        timeout: 15000,\n      });\n\n      if (response.data) {\n        const html = response.data;\n        \n        // Buscar enlaces de descarga\n        const videoMatch = html.match(/href=\"([^\"]*)\" download[^>]*>.*?Download/i);\n        const thumbnailMatch = html.match(/<img[^>]*src=\"([^\"]*)\"[^>]*>/);\n        \n        if (videoMatch) {\n          return {\n            success: true,\n            data: {\n              url: videoMatch[1],\n              thumbnail: thumbnailMatch ? thumbnailMatch[1] : undefined,\n              quality: 'Original'\n            }\n          };\n        }\n      }\n\n      return { success: false, error: 'No video found in API response' };\n    } catch (error) {\n      throw new Error('Public API 2 failed');\n    }\n  }\n\n  private static async tryPublicAPI3(url: string): Promise<DownloadResult> {\n    try {\n      // Usar una API alternativa\n      const response = await axios.get(`https://api.downloadgram.com/media?url=${encodeURIComponent(url)}`, {\n        headers: {\n          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',\n          'Accept': 'application/json',\n        },\n        timeout: 15000,\n      });\n\n      if (response.data && response.data.video_url) {\n        return {\n          success: true,\n          data: {\n            url: response.data.video_url,\n            thumbnail: response.data.thumbnail_url,\n            title: response.data.title,\n            quality: response.data.quality || 'HD'\n          }\n        };\n      }\n\n      return { success: false, error: 'No video found in API response' };\n    } catch (error) {\n      throw new Error('Public API 3 failed');\n    }\n  }\n\n  private static async tryDirectScraping(url: string): Promise<DownloadResult> {\n    try {\n      const response = await axios.get(url, {\n        headers: {\n          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',\n          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',\n          'Accept-Language': 'en-US,en;q=0.5',\n          'Accept-Encoding': 'gzip, deflate, br',\n          'Connection': 'keep-alive',\n          'Upgrade-Insecure-Requests': '1',\n        },\n        timeout: 15000,\n      });\n\n      const html = response.data;\n      \n      // Buscar patrones de video en el HTML\n      const patterns = [\n        /\"video_url\":\"([^\"]+)\"/,\n        /\"src\":\"([^\"]*\\.mp4[^\"]*)\"/,\n        /videoUrl\":\"([^\"]+)\"/,\n        /\"contentUrl\":\"([^\"]+\\.mp4[^\"]*)\"/,\n      ];\n\n      for (const pattern of patterns) {\n        const match = html.match(pattern);\n        if (match) {\n          const videoUrl = match[1].replace(/\\\\u0026/g, '&').replace(/\\\\/g, '');\n          \n          if (videoUrl && videoUrl.includes('.mp4')) {\n            // Buscar thumbnail\n            const thumbnailMatch = html.match(/\"display_url\":\"([^\"]+)\"/);\n            const thumbnail = thumbnailMatch ? thumbnailMatch[1].replace(/\\\\/g, '') : undefined;\n            \n            return {\n              success: true,\n              data: {\n                url: videoUrl,\n                thumbnail,\n                quality: 'Original'\n              }\n            };\n          }\n        }\n      }\n\n      return { success: false, error: 'No video found in HTML' };\n    } catch (error) {\n      throw new Error('Direct scraping failed');\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAaO,MAAM;IACX,OAAwB,qBAAqB;QAC3C;QACA;KACD,CAAC;IAEF,OAAwB,cAAc;QACpC;QACA;QACA;KACD,CAAC;IAEF,aAAa,uBAAuB,GAAW,EAA2B;QACxE,iBAAiB;QACjB,MAAM,WAAW,IAAI,CAAC,iBAAiB,CAAC;QAExC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,WAAW;YACvC,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,8BAA8B;QAC9B,MAAM,UAAU;YACd,IAAM,IAAI,CAAC,aAAa,CAAC;YACzB,IAAM,IAAI,CAAC,aAAa,CAAC;YACzB,IAAM,IAAI,CAAC,aAAa,CAAC;YACzB,IAAM,IAAI,CAAC,iBAAiB,CAAC;SAC9B;QAED,KAAK,MAAM,UAAU,QAAS;YAC5B,IAAI;gBACF,MAAM,SAAS,MAAM;gBACrB,IAAI,OAAO,OAAO,EAAE;oBAClB,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,iBAAiB;gBAC9B;YACF;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAe,kBAAkB,GAAW,EAAU;QACpD,iCAAiC;QACjC,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;IAC1B;IAEA,OAAe,oBAAoB,GAAW,EAAW;QACvD,MAAM,QAAQ;QACd,OAAO,MAAM,IAAI,CAAC;IACpB;IAEA,aAAqB,cAAc,GAAW,EAA2B;QACvE,IAAI;YACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,yCAChC,IAAI,gBAAgB;gBAClB,GAAG;gBACH,GAAG;gBACH,MAAM;YACR,IAAI;gBACJ,SAAS;oBACP,gBAAgB;oBAChB,cAAc;oBACd,UAAU;oBACV,oBAAoB;gBACtB;gBACA,SAAS;YACX;YAEA,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBACvC,MAAM,OAAO,SAAS,IAAI,CAAC,IAAI;gBAE/B,wBAAwB;gBACxB,MAAM,aAAa,KAAK,KAAK,CAAC;gBAC9B,MAAM,iBAAiB,KAAK,KAAK,CAAC;gBAElC,IAAI,YAAY;oBACd,OAAO;wBACL,SAAS;wBACT,MAAM;4BACJ,KAAK,UAAU,CAAC,EAAE;4BAClB,WAAW,iBAAiB,cAAc,CAAC,EAAE,GAAG;4BAChD,SAAS;wBACX;oBACF;gBACF;YACF;YAEA,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAiC;QACnE,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAqB,cAAc,GAAW,EAA2B;QACvE,IAAI;YACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,oCAChC,IAAI,gBAAgB;gBAClB,KAAK;gBACL,QAAQ;YACV,IAAI;gBACJ,SAAS;oBACP,gBAAgB;oBAChB,cAAc;oBACd,UAAU;gBACZ;gBACA,SAAS;YACX;YAEA,IAAI,SAAS,IAAI,EAAE;gBACjB,MAAM,OAAO,SAAS,IAAI;gBAE1B,6BAA6B;gBAC7B,MAAM,aAAa,KAAK,KAAK,CAAC;gBAC9B,MAAM,iBAAiB,KAAK,KAAK,CAAC;gBAElC,IAAI,YAAY;oBACd,OAAO;wBACL,SAAS;wBACT,MAAM;4BACJ,KAAK,UAAU,CAAC,EAAE;4BAClB,WAAW,iBAAiB,cAAc,CAAC,EAAE,GAAG;4BAChD,SAAS;wBACX;oBACF;gBACF;YACF;YAEA,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAiC;QACnE,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAqB,cAAc,GAAW,EAA2B;QACvE,IAAI;YACF,2BAA2B;YAC3B,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,uCAAuC,EAAE,mBAAmB,MAAM,EAAE;gBACpG,SAAS;oBACP,cAAc;oBACd,UAAU;gBACZ;gBACA,SAAS;YACX;YAEA,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,SAAS,EAAE;gBAC5C,OAAO;oBACL,SAAS;oBACT,MAAM;wBACJ,KAAK,SAAS,IAAI,CAAC,SAAS;wBAC5B,WAAW,SAAS,IAAI,CAAC,aAAa;wBACtC,OAAO,SAAS,IAAI,CAAC,KAAK;wBAC1B,SAAS,SAAS,IAAI,CAAC,OAAO,IAAI;oBACpC;gBACF;YACF;YAEA,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAiC;QACnE,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAqB,kBAAkB,GAAW,EAA2B;QAC3E,IAAI;YACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;gBACpC,SAAS;oBACP,cAAc;oBACd,UAAU;oBACV,mBAAmB;oBACnB,mBAAmB;oBACnB,cAAc;oBACd,6BAA6B;gBAC/B;gBACA,SAAS;YACX;YAEA,MAAM,OAAO,SAAS,IAAI;YAE1B,sCAAsC;YACtC,MAAM,WAAW;gBACf;gBACA;gBACA;gBACA;aACD;YAED,KAAK,MAAM,WAAW,SAAU;gBAC9B,MAAM,QAAQ,KAAK,KAAK,CAAC;gBACzB,IAAI,OAAO;oBACT,MAAM,WAAW,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,YAAY,KAAK,OAAO,CAAC,OAAO;oBAElE,IAAI,YAAY,SAAS,QAAQ,CAAC,SAAS;wBACzC,mBAAmB;wBACnB,MAAM,iBAAiB,KAAK,KAAK,CAAC;wBAClC,MAAM,YAAY,iBAAiB,cAAc,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,MAAM;wBAE1E,OAAO;4BACL,SAAS;4BACT,MAAM;gCACJ,KAAK;gCACL;gCACA,SAAS;4BACX;wBACF;oBACF;gBACF;YACF;YAEA,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyB;QAC3D,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 685, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/videostiktok/instagram-downloader/src/app/api/download/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { InstagramService } from '@/lib/instagram-service';\nimport { InstagramDownloader } from '@/lib/instagram-downloader';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { url } = await request.json();\n\n    if (!url) {\n      return NextResponse.json(\n        { error: 'URL es requerida' },\n        { status: 400 }\n      );\n    }\n\n    // Validar que sea una URL de Instagram\n    if (!InstagramService.isValidInstagramUrl(url)) {\n      return NextResponse.json(\n        { error: 'URL de Instagram no válida' },\n        { status: 400 }\n      );\n    }\n\n    try {\n      // Intentar primero con el nuevo downloader\n      const downloadResult = await InstagramDownloader.downloadInstagramVideo(url);\n\n      if (downloadResult.success && downloadResult.data) {\n        return NextResponse.json({\n          type: 'video',\n          qualities: [{\n            url: downloadResult.data.url,\n            quality: downloadResult.data.quality || 'HD',\n          }],\n          thumbnail: downloadResult.data.thumbnail,\n          caption: downloadResult.data.title || '',\n        });\n      }\n\n      // Si falla, intentar con el servicio original\n      const videoData = await InstagramService.getVideoData(url);\n      return NextResponse.json(videoData);\n\n    } catch (error: any) {\n      console.error('Error al obtener datos de Instagram:', error.message);\n\n      return NextResponse.json(\n        { error: error.message || 'No se pudo acceder al contenido de Instagram. El video podría ser privado o no estar disponible.' },\n        { status: 503 }\n      );\n    }\n\n  } catch (error: any) {\n    console.error('Error en la API:', error);\n    return NextResponse.json(\n      { error: 'Error interno del servidor' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,QAAQ,IAAI;QAElC,IAAI,CAAC,KAAK;YACR,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,uCAAuC;QACvC,IAAI,CAAC,oIAAA,CAAA,mBAAgB,CAAC,mBAAmB,CAAC,MAAM;YAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6B,GACtC;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI;YACF,2CAA2C;YAC3C,MAAM,iBAAiB,MAAM,uIAAA,CAAA,sBAAmB,CAAC,sBAAsB,CAAC;YAExE,IAAI,eAAe,OAAO,IAAI,eAAe,IAAI,EAAE;gBACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,MAAM;oBACN,WAAW;wBAAC;4BACV,KAAK,eAAe,IAAI,CAAC,GAAG;4BAC5B,SAAS,eAAe,IAAI,CAAC,OAAO,IAAI;wBAC1C;qBAAE;oBACF,WAAW,eAAe,IAAI,CAAC,SAAS;oBACxC,SAAS,eAAe,IAAI,CAAC,KAAK,IAAI;gBACxC;YACF;YAEA,8CAA8C;YAC9C,MAAM,YAAY,MAAM,oIAAA,CAAA,mBAAgB,CAAC,YAAY,CAAC;YACtD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAE3B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,wCAAwC,MAAM,OAAO;YAEnE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,MAAM,OAAO,IAAI;YAAmG,GAC7H;gBAAE,QAAQ;YAAI;QAElB;IAEF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA6B,GACtC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}