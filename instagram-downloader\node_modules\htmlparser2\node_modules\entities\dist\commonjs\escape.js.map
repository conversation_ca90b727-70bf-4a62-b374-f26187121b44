{"version": 3, "file": "escape.js", "sourceRoot": "", "sources": ["../../src/escape.ts"], "names": [], "mappings": ";;;AA+BA,8BA0BC;AAzDY,QAAA,WAAW,GAAW,wBAAwB,CAAC;AAE5D,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC;IACvB,CAAC,EAAE,EAAE,QAAQ,CAAC;IACd,CAAC,EAAE,EAAE,OAAO,CAAC;IACb,CAAC,EAAE,EAAE,QAAQ,CAAC;IACd,CAAC,EAAE,EAAE,MAAM,CAAC;IACZ,CAAC,EAAE,EAAE,MAAM,CAAC;CACf,CAAC,CAAC;AAEH,yDAAyD;AAC5C,QAAA,YAAY;AACrB,uEAAuE;AACvE,MAAM,CAAC,SAAS,CAAC,WAAW,IAAI,IAAI;IAChC,CAAC,CAAC,CAAC,CAAS,EAAE,KAAa,EAAU,EAAE,CACjC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,KAAO,CAAC,KAAK,KAAO;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,KAAO,CAAC,GAAG,IAAM;YACxC,CAAC,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC;YACvB,KAAO;YACP,KAAS;QACX,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC;IAC/B,CAAC,CAAC,uEAAuE;QACvE,CAAC,KAAa,EAAE,KAAa,EAAU,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAE,CAAC;AAE9E;;;;;;GAMG;AACH,SAAgB,SAAS,CAAC,KAAa;IACnC,IAAI,WAAW,GAAG,EAAE,CAAC;IACrB,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI,KAAK,CAAC;IAEV,OAAO,CAAC,KAAK,GAAG,mBAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;QAChD,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC;QACxB,MAAM,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACrC,MAAM,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAElC,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YACrB,WAAW,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,MAAM,IAAA,oBAAY,EACjE,KAAK,EACL,KAAK,CACR,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC;YAClB,4CAA4C;YAC5C,SAAS,GAAG,mBAAW,CAAC,SAAS,IAAI,MAAM,CACvC,CAAC,IAAI,GAAG,KAAO,CAAC,KAAK,KAAO,CAC/B,CAAC;QACN,CAAC;aAAM,CAAC;YACJ,WAAW,IAAI,KAAK,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC;YACxD,SAAS,GAAG,KAAK,GAAG,CAAC,CAAC;QAC1B,CAAC;IACL,CAAC;IAED,OAAO,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACjD,CAAC;AAED;;;;;;;;GAQG;AACU,QAAA,MAAM,GAAqB,SAAS,CAAC;AAElD;;;;;;;;;GASG;AACH,SAAS,UAAU,CACf,KAAa,EACb,GAAwB;IAExB,OAAO,SAAS,MAAM,CAAC,IAAY;QAC/B,IAAI,KAAK,CAAC;QACV,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;YAChC,IAAI,SAAS,KAAK,KAAK,CAAC,KAAK,EAAE,CAAC;gBAC5B,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACrD,CAAC;YAED,kDAAkD;YAClD,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAE,CAAC;YAE3C,kCAAkC;YAClC,SAAS,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;QAChC,CAAC;QAED,OAAO,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC9C,CAAC,CAAC;AACN,CAAC;AAED;;;;;;GAMG;AACU,QAAA,UAAU,GAA6C,UAAU,CAC1E,UAAU,EACV,UAAU,CACb,CAAC;AAEF;;;;;GAKG;AACU,QAAA,eAAe;AACxB,eAAe,CAAC,UAAU,CACtB,aAAa,EACb,IAAI,GAAG,CAAC;IACJ,CAAC,EAAE,EAAE,QAAQ,CAAC;IACd,CAAC,EAAE,EAAE,OAAO,CAAC;IACb,CAAC,GAAG,EAAE,QAAQ,CAAC;CAClB,CAAC,CACL,CAAC;AAEN;;;;;GAKG;AACU,QAAA,UAAU,GAA6C,UAAU,CAC1E,cAAc,EACd,IAAI,GAAG,CAAC;IACJ,CAAC,EAAE,EAAE,OAAO,CAAC;IACb,CAAC,EAAE,EAAE,MAAM,CAAC;IACZ,CAAC,EAAE,EAAE,MAAM,CAAC;IACZ,CAAC,GAAG,EAAE,QAAQ,CAAC;CAClB,CAAC,CACL,CAAC"}