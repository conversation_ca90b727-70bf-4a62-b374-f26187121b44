import axios from 'axios';

export interface DownloadResult {
  success: boolean;
  data?: {
    url: string;
    thumbnail?: string;
    title?: string;
    quality?: string;
  };
  error?: string;
}

export class InstagramDownloader {
  private static readonly RAPIDAPI_ENDPOINTS = [
    'https://instagram-downloader-download-instagram-videos-stories.p.rapidapi.com/index',
    'https://instagram-bulk-profile-scrapper.p.rapidapi.com/clients/api/ig/ig_profile',
  ];

  private static readonly PUBLIC_APIS = [
    'https://api.downloadgram.com/media',
    'https://www.saveig.app/api/ajaxSearch',
    'https://snapinsta.app/action.php',
  ];

  static async downloadInstagramVideo(url: string): Promise<DownloadResult> {
    // Limpiar la URL
    const cleanUrl = this.cleanInstagramUrl(url);
    
    if (!this.isValidInstagramUrl(cleanUrl)) {
      return {
        success: false,
        error: 'URL de Instagram no válida'
      };
    }

    // Intentar diferentes métodos
    const methods = [
      () => this.tryPublicAPI1(cleanUrl),
      () => this.tryPublicAPI2(cleanUrl),
      () => this.tryPublicAPI3(cleanUrl),
      () => this.tryDirectScraping(cleanUrl),
    ];

    for (const method of methods) {
      try {
        const result = await method();
        if (result.success) {
          return result;
        }
      } catch (error) {
        console.warn('Método falló:', error);
        continue;
      }
    }

    return {
      success: false,
      error: 'No se pudo descargar el video. El contenido podría ser privado o no estar disponible.'
    };
  }

  private static cleanInstagramUrl(url: string): string {
    // Remover parámetros de tracking
    return url.split('?')[0];
  }

  private static isValidInstagramUrl(url: string): boolean {
    const regex = /^https?:\/\/(www\.)?(instagram\.com|instagr\.am)\/(p|reel|tv)\/[A-Za-z0-9_-]+/;
    return regex.test(url);
  }

  private static async tryPublicAPI1(url: string): Promise<DownloadResult> {
    try {
      const response = await axios.post('https://www.saveig.app/api/ajaxSearch', 
        new URLSearchParams({
          q: url,
          t: 'media',
          lang: 'en'
        }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': '*/*',
          'X-Requested-With': 'XMLHttpRequest',
        },
        timeout: 15000,
      });

      if (response.data && response.data.data) {
        const html = response.data.data;
        
        // Extraer URL del video
        const videoMatch = html.match(/href="([^"]*\.mp4[^"]*)"/);
        const thumbnailMatch = html.match(/src="([^"]*\.(jpg|jpeg|png)[^"]*)"/);
        
        if (videoMatch) {
          return {
            success: true,
            data: {
              url: videoMatch[1],
              thumbnail: thumbnailMatch ? thumbnailMatch[1] : undefined,
              quality: 'HD'
            }
          };
        }
      }

      return { success: false, error: 'No video found in API response' };
    } catch (error) {
      throw new Error('Public API 1 failed');
    }
  }

  private static async tryPublicAPI2(url: string): Promise<DownloadResult> {
    try {
      const response = await axios.post('https://snapinsta.app/action.php', 
        new URLSearchParams({
          url: url,
          action: 'post'
        }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        },
        timeout: 15000,
      });

      if (response.data) {
        const html = response.data;
        
        // Buscar enlaces de descarga
        const videoMatch = html.match(/href="([^"]*)" download[^>]*>.*?Download/i);
        const thumbnailMatch = html.match(/<img[^>]*src="([^"]*)"[^>]*>/);
        
        if (videoMatch) {
          return {
            success: true,
            data: {
              url: videoMatch[1],
              thumbnail: thumbnailMatch ? thumbnailMatch[1] : undefined,
              quality: 'Original'
            }
          };
        }
      }

      return { success: false, error: 'No video found in API response' };
    } catch (error) {
      throw new Error('Public API 2 failed');
    }
  }

  private static async tryPublicAPI3(url: string): Promise<DownloadResult> {
    try {
      // Usar una API alternativa
      const response = await axios.get(`https://api.downloadgram.com/media?url=${encodeURIComponent(url)}`, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
          'Accept': 'application/json',
        },
        timeout: 15000,
      });

      if (response.data && response.data.video_url) {
        return {
          success: true,
          data: {
            url: response.data.video_url,
            thumbnail: response.data.thumbnail_url,
            title: response.data.title,
            quality: response.data.quality || 'HD'
          }
        };
      }

      return { success: false, error: 'No video found in API response' };
    } catch (error) {
      throw new Error('Public API 3 failed');
    }
  }

  private static async tryDirectScraping(url: string): Promise<DownloadResult> {
    try {
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate, br',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
        },
        timeout: 15000,
      });

      const html = response.data;
      
      // Buscar patrones de video en el HTML
      const patterns = [
        /"video_url":"([^"]+)"/,
        /"src":"([^"]*\.mp4[^"]*)"/,
        /videoUrl":"([^"]+)"/,
        /"contentUrl":"([^"]+\.mp4[^"]*)"/,
      ];

      for (const pattern of patterns) {
        const match = html.match(pattern);
        if (match) {
          const videoUrl = match[1].replace(/\\u0026/g, '&').replace(/\\/g, '');
          
          if (videoUrl && videoUrl.includes('.mp4')) {
            // Buscar thumbnail
            const thumbnailMatch = html.match(/"display_url":"([^"]+)"/);
            const thumbnail = thumbnailMatch ? thumbnailMatch[1].replace(/\\/g, '') : undefined;
            
            return {
              success: true,
              data: {
                url: videoUrl,
                thumbnail,
                quality: 'Original'
              }
            };
          }
        }
      }

      return { success: false, error: 'No video found in HTML' };
    } catch (error) {
      throw new Error('Direct scraping failed');
    }
  }
}
