module.exports = {

"[project]/.next-internal/server/app/api/download/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/lib/working-instagram-downloader.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "WorkingInstagramDownloader": (()=>WorkingInstagramDownloader)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
;
class WorkingInstagramDownloader {
    static async downloadVideo(url) {
        const cleanUrl = url.split('?')[0];
        // Método 1: API de Insta Loader
        const result1 = await this.tryInstaLoader(cleanUrl);
        if (result1.success) return result1;
        // Método 2: API de Y2Mate
        const result2 = await this.tryY2Mate(cleanUrl);
        if (result2.success) return result2;
        // Método 3: API de SSS Instagram
        const result3 = await this.trySSSInstagram(cleanUrl);
        if (result3.success) return result3;
        // Método 4: API de InstaSave
        const result4 = await this.tryInstaSave(cleanUrl);
        if (result4.success) return result4;
        return {
            success: false,
            error: 'No se pudo descargar el video. Inténtalo con otra URL.'
        };
    }
    static async tryInstaLoader(url) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].post('https://instaloader.io/api/post', {
                url: url
            }, {
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Referer': 'https://instaloader.io/',
                    'Origin': 'https://instaloader.io'
                },
                timeout: 20000
            });
            if (response.data && response.data.video_url) {
                return {
                    success: true,
                    data: {
                        type: 'video',
                        qualities: [
                            {
                                url: response.data.video_url,
                                quality: 'HD'
                            }
                        ],
                        thumbnail: response.data.thumbnail_url,
                        caption: response.data.caption || ''
                    }
                };
            }
            return {
                success: false
            };
        } catch (error) {
            return {
                success: false
            };
        }
    }
    static async tryY2Mate(url) {
        try {
            // Paso 1: Analizar la URL
            const analyzeResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].post('https://www.y2mate.com/mates/analyzeV2/ajax', new URLSearchParams({
                k_query: url,
                k_page: 'home',
                hl: 'en',
                q_auto: '0'
            }), {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Referer': 'https://www.y2mate.com/',
                    'Origin': 'https://www.y2mate.com'
                },
                timeout: 20000
            });
            if (analyzeResponse.data && analyzeResponse.data.links) {
                const videoLinks = analyzeResponse.data.links.mp4;
                if (videoLinks) {
                    const qualities = Object.keys(videoLinks).map((quality)=>({
                            url: videoLinks[quality].url,
                            quality: quality
                        }));
                    return {
                        success: true,
                        data: {
                            type: 'video',
                            qualities,
                            thumbnail: analyzeResponse.data.thumbnail,
                            caption: analyzeResponse.data.title || ''
                        }
                    };
                }
            }
            return {
                success: false
            };
        } catch (error) {
            return {
                success: false
            };
        }
    }
    static async trySSSInstagram(url) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].post('https://sssinstagram.com/api/ig/post', {
                url: url
            }, {
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15',
                    'Referer': 'https://sssinstagram.com/',
                    'Origin': 'https://sssinstagram.com'
                },
                timeout: 20000
            });
            if (response.data && response.data.data && response.data.data.length > 0) {
                const videoData = response.data.data[0];
                if (videoData.video_url) {
                    return {
                        success: true,
                        data: {
                            type: 'video',
                            qualities: [
                                {
                                    url: videoData.video_url,
                                    quality: 'HD'
                                }
                            ],
                            thumbnail: videoData.thumbnail_url,
                            caption: videoData.caption || ''
                        }
                    };
                }
            }
            return {
                success: false
            };
        } catch (error) {
            return {
                success: false
            };
        }
    }
    static async tryInstaSave(url) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].post('https://instasave.website/system/action.php', new URLSearchParams({
                url: url,
                action: 'post'
            }), {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                    'Referer': 'https://instasave.website/',
                    'Origin': 'https://instasave.website'
                },
                timeout: 20000
            });
            if (response.data) {
                const html = response.data;
                // Buscar enlaces de descarga en el HTML
                const videoMatch = html.match(/href="([^"]*\.mp4[^"]*)"/);
                const thumbnailMatch = html.match(/src="([^"]*\.(jpg|jpeg|png)[^"]*)"/);
                if (videoMatch && videoMatch[1]) {
                    return {
                        success: true,
                        data: {
                            type: 'video',
                            qualities: [
                                {
                                    url: videoMatch[1],
                                    quality: 'HD'
                                }
                            ],
                            thumbnail: thumbnailMatch ? thumbnailMatch[1] : undefined,
                            caption: ''
                        }
                    };
                }
            }
            return {
                success: false
            };
        } catch (error) {
            return {
                success: false
            };
        }
    }
    // Método adicional usando proxy CORS
    static async tryWithProxy(url) {
        try {
            const proxyUrl = `https://api.allorigins.win/raw?url=${encodeURIComponent(url)}`;
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(proxyUrl, {
                headers: {
                    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15'
                },
                timeout: 20000
            });
            const html = response.data;
            // Buscar patrones de video
            const patterns = [
                /"video_url":"([^"]+)"/,
                /"playback_url":"([^"]+)"/,
                /"src":"([^"]*\.mp4[^"]*)"/,
                /videoUrl":"([^"]+)"/
            ];
            for (const pattern of patterns){
                const match = html.match(pattern);
                if (match) {
                    const videoUrl = match[1].replace(/\\u0026/g, '&').replace(/\\/g, '');
                    if (videoUrl && videoUrl.includes('.mp4')) {
                        const thumbnailMatch = html.match(/"display_url":"([^"]+)"/);
                        return {
                            success: true,
                            data: {
                                type: 'video',
                                qualities: [
                                    {
                                        url: videoUrl,
                                        quality: 'Original'
                                    }
                                ],
                                thumbnail: thumbnailMatch ? thumbnailMatch[1].replace(/\\/g, '') : undefined,
                                caption: ''
                            }
                        };
                    }
                }
            }
            return {
                success: false
            };
        } catch (error) {
            return {
                success: false
            };
        }
    }
}
}}),
"[project]/src/app/api/download/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$working$2d$instagram$2d$downloader$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/working-instagram-downloader.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
;
;
;
async function POST(request) {
    try {
        const { url } = await request.json();
        if (!url) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'URL es requerida'
            }, {
                status: 400
            });
        }
        // Validar que sea una URL de Instagram
        const instagramRegex = /^https?:\/\/(www\.)?(instagram\.com|instagr\.am)\/(p|reel|tv)\/[A-Za-z0-9_-]+/;
        if (!instagramRegex.test(url)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'URL de Instagram no válida'
            }, {
                status: 400
            });
        }
        // Limpiar la URL
        const cleanUrl = url.split('?')[0];
        try {
            // Método principal: Usar el downloader que funciona
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$working$2d$instagram$2d$downloader$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["WorkingInstagramDownloader"].downloadVideo(cleanUrl);
            if (result.success && result.data) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(result.data);
            }
            // Método de respaldo: Usar proxy
            const proxyResult = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$working$2d$instagram$2d$downloader$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["WorkingInstagramDownloader"].tryWithProxy(cleanUrl);
            if (proxyResult.success && proxyResult.data) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(proxyResult.data);
            }
            // Método adicional: API de SaveIG (mejorada)
            const saveIgResult = await tryImprovedSaveIG(cleanUrl);
            if (saveIgResult.success) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(saveIgResult.data);
            }
            // Método final: Scraping directo mejorado
            const scrapingResult = await tryImprovedScraping(cleanUrl);
            if (scrapingResult.success) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(scrapingResult.data);
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No se pudo extraer el video. El contenido podría ser privado o no estar disponible.'
            }, {
                status: 404
            });
        } catch (error) {
            console.error('Error al procesar video:', error);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Error al procesar el video. Inténtalo de nuevo.'
            }, {
                status: 503
            });
        }
    } catch (error) {
        console.error('Error en la API:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Error interno del servidor'
        }, {
            status: 500
        });
    }
}
// Método mejorado de SaveIG
async function tryImprovedSaveIG(url) {
    try {
        // Primero obtener la página para conseguir tokens
        const pageResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get('https://www.saveig.app/', {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
        });
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].post('https://www.saveig.app/api/ajaxSearch', new URLSearchParams({
            q: url,
            t: 'media',
            lang: 'en'
        }), {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'X-Requested-With': 'XMLHttpRequest',
                'Referer': 'https://www.saveig.app/',
                'Origin': 'https://www.saveig.app',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin'
            },
            timeout: 20000
        });
        if (response.data && response.data.data) {
            const html = response.data.data;
            // Buscar múltiples patrones de enlaces de descarga
            const patterns = [
                /href="([^"]*\.mp4[^"]*)"/g,
                /data-href="([^"]*\.mp4[^"]*)"/g,
                /src="([^"]*\.mp4[^"]*)"/g
            ];
            const videoUrls = [];
            for (const pattern of patterns){
                const matches = [
                    ...html.matchAll(pattern)
                ];
                for (const match of matches){
                    if (match[1] && match[1].includes('.mp4') && !match[1].includes('javascript:')) {
                        videoUrls.push(match[1]);
                    }
                }
            }
            const thumbnailMatch = html.match(/src="([^"]*\.(jpg|jpeg|png)[^"]*)"/);
            if (videoUrls.length > 0) {
                const qualities = videoUrls.map((url, index)=>({
                        url: url,
                        quality: index === 0 ? 'HD' : 'SD'
                    }));
                return {
                    success: true,
                    data: {
                        type: 'video',
                        qualities,
                        thumbnail: thumbnailMatch ? thumbnailMatch[1] : null,
                        caption: ''
                    }
                };
            }
        }
        return {
            success: false
        };
    } catch (error) {
        console.error('SaveIG error:', error);
        return {
            success: false
        };
    }
}
// Método de scraping mejorado
async function tryImprovedScraping(url) {
    try {
        const userAgents = [
            'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Android 11; Mobile; rv:68.0) Gecko/68.0 Firefox/88.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15'
        ];
        for (const userAgent of userAgents){
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(url, {
                    headers: {
                        'User-Agent': userAgent,
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                        'Accept-Language': 'en-US,en;q=0.9',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'Connection': 'keep-alive',
                        'Upgrade-Insecure-Requests': '1',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'none',
                        'Cache-Control': 'max-age=0'
                    },
                    timeout: 20000
                });
                const html = response.data;
                // Patrones mejorados para encontrar videos
                const patterns = [
                    /"video_url":"([^"]+)"/g,
                    /"playback_url":"([^"]+)"/g,
                    /"src":"([^"]*\.mp4[^"]*)"/g,
                    /videoUrl":"([^"]+)"/g,
                    /"contentUrl":"([^"]+\.mp4[^"]*)"/g,
                    /video_url\\?":\\?"([^"]+)\\?"/g,
                    /"video_versions":\[{"url":"([^"]+)"/g,
                    /"dash_manifest":"([^"]+)"/g
                ];
                for (const pattern of patterns){
                    const matches = [
                        ...html.matchAll(pattern)
                    ];
                    for (const match of matches){
                        if (match[1]) {
                            let videoUrl = match[1].replace(/\\u0026/g, '&').replace(/\\/g, '');
                            if (videoUrl && (videoUrl.includes('.mp4') || videoUrl.includes('video')) && !videoUrl.includes('blob:') && !videoUrl.includes('javascript:')) {
                                // Buscar thumbnail
                                const thumbnailPatterns = [
                                    /"display_url":"([^"]+)"/,
                                    /"thumbnail_url":"([^"]+)"/,
                                    /"image_versions2":{"candidates":\[{"url":"([^"]+)"/
                                ];
                                let thumbnail = null;
                                for (const thumbPattern of thumbnailPatterns){
                                    const thumbMatch = html.match(thumbPattern);
                                    if (thumbMatch) {
                                        thumbnail = thumbMatch[1].replace(/\\/g, '');
                                        break;
                                    }
                                }
                                return {
                                    success: true,
                                    data: {
                                        type: 'video',
                                        qualities: [
                                            {
                                                url: videoUrl,
                                                quality: 'Original'
                                            }
                                        ],
                                        thumbnail,
                                        caption: ''
                                    }
                                };
                            }
                        }
                    }
                }
            } catch (scrapingError) {
                console.warn(`Scraping with ${userAgent} failed:`, scrapingError);
                continue;
            }
        }
        return {
            success: false
        };
    } catch (error) {
        console.error('Improved scraping error:', error);
        return {
            success: false
        };
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__4fb0fca7._.js.map