import axios from 'axios';

export class InstagramReverseEngineer {
  
  // Método que simula el comportamiento de un navegador real
  static async extractWithBrowserSimulation(url: string) {
    try {
      console.log('🕵️ Iniciando simulación de navegador...');
      
      // Paso 1: Obtener cookies y headers como un navegador real
      const initialResponse = await axios.get('https://www.instagram.com/', {
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate, br',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
          'Sec-Fetch-Dest': 'document',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'none'
        },
        timeout: 15000
      });

      // Extraer cookies importantes
      const cookies = initialResponse.headers['set-cookie']?.join('; ') || '';
      
      // Paso 2: Acceder al post específico con las cookies
      const postResponse = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate, br',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
          'Cookie': cookies,
          'Referer': 'https://www.instagram.com/',
          'Sec-Fetch-Dest': 'document',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'same-origin'
        },
        timeout: 15000
      });

      const html = postResponse.data;
      
      // Buscar datos JSON embebidos en el HTML
      const patterns = [
        /window\._sharedData\s*=\s*({.+?});/,
        /window\.__additionalDataLoaded\([^,]+,\s*({.+?})\);/,
        /"video_url":"([^"]+)"/,
        /"playback_url":"([^"]+)"/
      ];

      for (const pattern of patterns) {
        const match = html.match(pattern);
        if (match) {
          if (pattern.source.includes('window')) {
            // Es un objeto JSON
            try {
              const data = JSON.parse(match[1]);
              const videoUrl = this.extractVideoFromData(data);
              if (videoUrl) {
                return {
                  success: true,
                  videoUrl: videoUrl,
                  thumbnail: this.extractThumbnailFromData(data),
                  title: 'Video extraído con simulación de navegador'
                };
              }
            } catch (e) {
              continue;
            }
          } else {
            // Es una URL directa
            const videoUrl = match[1].replace(/\\u0026/g, '&').replace(/\\/g, '');
            if (videoUrl && videoUrl.includes('.mp4')) {
              return {
                success: true,
                videoUrl: videoUrl,
                thumbnail: null,
                title: 'Video extraído con simulación de navegador'
              };
            }
          }
        }
      }

      return { success: false };
    } catch (error) {
      console.error('Error en simulación de navegador:', error);
      return { success: false };
    }
  }

  // Método que usa APIs no documentadas de Instagram
  static async extractWithUndocumentedAPI(url: string) {
    try {
      console.log('🔓 Intentando APIs no documentadas...');
      
      const shortcode = this.extractShortcode(url);
      if (!shortcode) return { success: false };

      // API no documentada 1: Instagram Web API
      try {
        const apiUrl = `https://www.instagram.com/api/v1/media/${shortcode}/info/`;
        const response = await axios.get(apiUrl, {
          headers: {
            'User-Agent': 'Instagram 219.0.0.12.117 Android',
            'Accept': 'application/json',
            'X-IG-App-ID': '936619743392459'
          },
          timeout: 15000
        });

        if (response.data && response.data.items && response.data.items[0]) {
          const item = response.data.items[0];
          if (item.video_versions && item.video_versions.length > 0) {
            return {
              success: true,
              videoUrl: item.video_versions[0].url,
              thumbnail: item.image_versions2?.candidates?.[0]?.url,
              title: item.caption?.text || 'Video de Instagram'
            };
          }
        }
      } catch (e) {
        console.log('API no documentada 1 falló');
      }

      // API no documentada 2: GraphQL endpoint
      try {
        const graphqlUrl = 'https://www.instagram.com/graphql/query/';
        const response = await axios.post(graphqlUrl, {
          query_hash: 'b3055c01b4b222b8a47dc12b090e4e64',
          variables: JSON.stringify({
            shortcode: shortcode,
            child_comment_count: 3,
            fetch_comment_count: 40,
            parent_comment_count: 24,
            has_threaded_comments: true
          })
        }, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15',
            'X-Requested-With': 'XMLHttpRequest',
            'X-IG-App-ID': '936619743392459'
          },
          timeout: 15000
        });

        if (response.data?.data?.shortcode_media) {
          const media = response.data.data.shortcode_media;
          if (media.is_video && media.video_url) {
            return {
              success: true,
              videoUrl: media.video_url,
              thumbnail: media.display_url,
              title: media.edge_media_to_caption?.edges?.[0]?.node?.text || 'Video de Instagram'
            };
          }
        }
      } catch (e) {
        console.log('API no documentada 2 falló');
      }

      return { success: false };
    } catch (error) {
      console.error('Error en APIs no documentadas:', error);
      return { success: false };
    }
  }

  // Método que usa técnicas de bypass de CORS
  static async extractWithCORSBypass(url: string) {
    try {
      console.log('🌐 Intentando bypass de CORS...');
      
      const corsProxies = [
        'https://api.allorigins.win/get?url=',
        'https://cors-anywhere.herokuapp.com/',
        'https://thingproxy.freeboard.io/fetch/',
        'https://api.codetabs.com/v1/proxy?quest='
      ];

      for (const proxy of corsProxies) {
        try {
          const proxyUrl = proxy + encodeURIComponent(url);
          const response = await axios.get(proxyUrl, {
            headers: {
              'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15'
            },
            timeout: 10000
          });

          let html = response.data;
          
          // Algunos proxies devuelven un objeto con la respuesta en 'contents'
          if (typeof html === 'object' && html.contents) {
            html = html.contents;
          }

          // Buscar videos en el HTML
          const videoPatterns = [
            /"video_url":"([^"]+)"/,
            /"playback_url":"([^"]+)"/,
            /"video_versions":\[{"url":"([^"]+)"/,
            /"src":"([^"]*\.mp4[^"]*)"/
          ];

          for (const pattern of videoPatterns) {
            const match = html.match(pattern);
            if (match && match[1]) {
              const videoUrl = match[1].replace(/\\u0026/g, '&').replace(/\\/g, '');
              if (videoUrl.includes('.mp4') && !videoUrl.includes('blob:')) {
                const thumbnailMatch = html.match(/"display_url":"([^"]+)"/);
                return {
                  success: true,
                  videoUrl: videoUrl,
                  thumbnail: thumbnailMatch ? thumbnailMatch[1].replace(/\\/g, '') : null,
                  title: 'Video extraído con bypass de CORS'
                };
              }
            }
          }
        } catch (proxyError) {
          continue;
        }
      }

      return { success: false };
    } catch (error) {
      console.error('Error en bypass de CORS:', error);
      return { success: false };
    }
  }

  private static extractShortcode(url: string): string | null {
    const match = url.match(/\/(p|reel|tv)\/([A-Za-z0-9_-]+)/);
    return match ? match[2] : null;
  }

  private static extractVideoFromData(data: any): string | null {
    try {
      // Buscar en diferentes estructuras de datos de Instagram
      if (data.entry_data?.PostPage?.[0]?.graphql?.shortcode_media) {
        const media = data.entry_data.PostPage[0].graphql.shortcode_media;
        if (media.is_video && media.video_url) {
          return media.video_url;
        }
      }

      if (data.items?.[0]?.video_versions?.[0]?.url) {
        return data.items[0].video_versions[0].url;
      }

      // Buscar recursivamente en el objeto
      const findVideo = (obj: any): string | null => {
        if (typeof obj !== 'object' || obj === null) return null;
        
        for (const key in obj) {
          if (key === 'video_url' && typeof obj[key] === 'string') {
            return obj[key];
          }
          if (key === 'playback_url' && typeof obj[key] === 'string') {
            return obj[key];
          }
          if (typeof obj[key] === 'object') {
            const result = findVideo(obj[key]);
            if (result) return result;
          }
        }
        return null;
      };

      return findVideo(data);
    } catch (error) {
      return null;
    }
  }

  private static extractThumbnailFromData(data: any): string | null {
    try {
      if (data.entry_data?.PostPage?.[0]?.graphql?.shortcode_media?.display_url) {
        return data.entry_data.PostPage[0].graphql.shortcode_media.display_url;
      }

      if (data.items?.[0]?.image_versions2?.candidates?.[0]?.url) {
        return data.items[0].image_versions2.candidates[0].url;
      }

      return null;
    } catch (error) {
      return null;
    }
  }
}
