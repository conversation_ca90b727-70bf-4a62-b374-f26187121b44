{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/videostiktok/instagram-downloader/src/app/api/download/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport axios from 'axios';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { url } = await request.json();\n\n    if (!url) {\n      return NextResponse.json(\n        { error: 'URL es requerida' },\n        { status: 400 }\n      );\n    }\n\n    // Validar que sea una URL de Instagram\n    const instagramRegex = /^https?:\\/\\/(www\\.)?(instagram\\.com|instagr\\.am)\\/(p|reel|tv)\\/[A-Za-z0-9_-]+/;\n    if (!instagramRegex.test(url)) {\n      return NextResponse.json(\n        { error: 'URL de Instagram no válida' },\n        { status: 400 }\n      );\n    }\n\n    // Limpiar la URL\n    const cleanUrl = url.split('?')[0];\n\n    try {\n      // Método 1: Usar API pública de SaveIG\n      try {\n        const response = await axios.post('https://www.saveig.app/api/ajaxSearch',\n          new URLSearchParams({\n            q: cleanUrl,\n            t: 'media',\n            lang: 'en'\n          }), {\n          headers: {\n            'Content-Type': 'application/x-www-form-urlencoded',\n            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',\n            'Accept': '*/*',\n            'X-Requested-With': 'XMLHttpRequest',\n            'Referer': 'https://www.saveig.app/',\n          },\n          timeout: 15000,\n        });\n\n        if (response.data && response.data.data) {\n          const html = response.data.data;\n\n          // Extraer URL del video\n          const videoMatch = html.match(/href=\"([^\"]*\\.mp4[^\"]*)\"/);\n          const thumbnailMatch = html.match(/src=\"([^\"]*\\.(jpg|jpeg|png)[^\"]*)\"/);\n\n          if (videoMatch) {\n            return NextResponse.json({\n              type: 'video',\n              qualities: [{\n                url: videoMatch[1],\n                quality: 'HD',\n              }],\n              thumbnail: thumbnailMatch ? thumbnailMatch[1] : null,\n              caption: '',\n            });\n          }\n        }\n      } catch (apiError) {\n        console.warn('SaveIG API failed:', apiError);\n      }\n\n      // Método 2: Scraping directo con diferentes User Agents\n      const userAgents = [\n        'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',\n        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',\n        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'\n      ];\n\n      for (const userAgent of userAgents) {\n        try {\n          const response = await axios.get(cleanUrl, {\n            headers: {\n              'User-Agent': userAgent,\n              'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',\n              'Accept-Language': 'en-US,en;q=0.5',\n              'Accept-Encoding': 'gzip, deflate, br',\n              'Connection': 'keep-alive',\n              'Upgrade-Insecure-Requests': '1',\n            },\n            timeout: 15000,\n          });\n\n          const html = response.data;\n\n          // Buscar patrones de video en el HTML\n          const patterns = [\n            /\"video_url\":\"([^\"]+)\"/,\n            /\"src\":\"([^\"]*\\.mp4[^\"]*)\"/,\n            /videoUrl\":\"([^\"]+)\"/,\n            /\"contentUrl\":\"([^\"]+\\.mp4[^\"]*)\"/,\n            /video_url\\\\?\":\\\\?\"([^\"]+)\\\\?\"/,\n          ];\n\n          for (const pattern of patterns) {\n            const match = html.match(pattern);\n            if (match) {\n              const videoUrl = match[1].replace(/\\\\u0026/g, '&').replace(/\\\\/g, '');\n\n              if (videoUrl && videoUrl.includes('.mp4')) {\n                // Buscar thumbnail\n                const thumbnailMatch = html.match(/\"display_url\":\"([^\"]+)\"/);\n                const thumbnail = thumbnailMatch ? thumbnailMatch[1].replace(/\\\\/g, '') : null;\n\n                return NextResponse.json({\n                  type: 'video',\n                  qualities: [{\n                    url: videoUrl,\n                    quality: 'Original',\n                  }],\n                  thumbnail,\n                  caption: '',\n                });\n              }\n            }\n          }\n        } catch (scrapingError) {\n          console.warn(`Scraping with ${userAgent} failed:`, scrapingError);\n          continue;\n        }\n      }\n\n      return NextResponse.json(\n        { error: 'No se pudo extraer el video de Instagram. El contenido podría ser privado, no estar disponible, o Instagram ha bloqueado el acceso.' },\n        { status: 404 }\n      );\n\n    } catch (error: any) {\n      console.error('Error al obtener datos de Instagram:', error.message);\n\n      return NextResponse.json(\n        { error: 'Error al procesar la solicitud. Inténtalo de nuevo más tarde.' },\n        { status: 503 }\n      );\n    }\n\n  } catch (error: any) {\n    console.error('Error en la API:', error);\n    return NextResponse.json(\n      { error: 'Error interno del servidor' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,QAAQ,IAAI;QAElC,IAAI,CAAC,KAAK;YACR,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,uCAAuC;QACvC,MAAM,iBAAiB;QACvB,IAAI,CAAC,eAAe,IAAI,CAAC,MAAM;YAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6B,GACtC;gBAAE,QAAQ;YAAI;QAElB;QAEA,iBAAiB;QACjB,MAAM,WAAW,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;QAElC,IAAI;YACF,uCAAuC;YACvC,IAAI;gBACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,yCAChC,IAAI,gBAAgB;oBAClB,GAAG;oBACH,GAAG;oBACH,MAAM;gBACR,IAAI;oBACJ,SAAS;wBACP,gBAAgB;wBAChB,cAAc;wBACd,UAAU;wBACV,oBAAoB;wBACpB,WAAW;oBACb;oBACA,SAAS;gBACX;gBAEA,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;oBACvC,MAAM,OAAO,SAAS,IAAI,CAAC,IAAI;oBAE/B,wBAAwB;oBACxB,MAAM,aAAa,KAAK,KAAK,CAAC;oBAC9B,MAAM,iBAAiB,KAAK,KAAK,CAAC;oBAElC,IAAI,YAAY;wBACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;4BACvB,MAAM;4BACN,WAAW;gCAAC;oCACV,KAAK,UAAU,CAAC,EAAE;oCAClB,SAAS;gCACX;6BAAE;4BACF,WAAW,iBAAiB,cAAc,CAAC,EAAE,GAAG;4BAChD,SAAS;wBACX;oBACF;gBACF;YACF,EAAE,OAAO,UAAU;gBACjB,QAAQ,IAAI,CAAC,sBAAsB;YACrC;YAEA,wDAAwD;YACxD,MAAM,aAAa;gBACjB;gBACA;gBACA;aACD;YAED,KAAK,MAAM,aAAa,WAAY;gBAClC,IAAI;oBACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,UAAU;wBACzC,SAAS;4BACP,cAAc;4BACd,UAAU;4BACV,mBAAmB;4BACnB,mBAAmB;4BACnB,cAAc;4BACd,6BAA6B;wBAC/B;wBACA,SAAS;oBACX;oBAEA,MAAM,OAAO,SAAS,IAAI;oBAE1B,sCAAsC;oBACtC,MAAM,WAAW;wBACf;wBACA;wBACA;wBACA;wBACA;qBACD;oBAED,KAAK,MAAM,WAAW,SAAU;wBAC9B,MAAM,QAAQ,KAAK,KAAK,CAAC;wBACzB,IAAI,OAAO;4BACT,MAAM,WAAW,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,YAAY,KAAK,OAAO,CAAC,OAAO;4BAElE,IAAI,YAAY,SAAS,QAAQ,CAAC,SAAS;gCACzC,mBAAmB;gCACnB,MAAM,iBAAiB,KAAK,KAAK,CAAC;gCAClC,MAAM,YAAY,iBAAiB,cAAc,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,MAAM;gCAE1E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oCACvB,MAAM;oCACN,WAAW;wCAAC;4CACV,KAAK;4CACL,SAAS;wCACX;qCAAE;oCACF;oCACA,SAAS;gCACX;4BACF;wBACF;oBACF;gBACF,EAAE,OAAO,eAAe;oBACtB,QAAQ,IAAI,CAAC,CAAC,cAAc,EAAE,UAAU,QAAQ,CAAC,EAAE;oBACnD;gBACF;YACF;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsI,GAC/I;gBAAE,QAAQ;YAAI;QAGlB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,wCAAwC,MAAM,OAAO;YAEnE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgE,GACzE;gBAAE,QAAQ;YAAI;QAElB;IAEF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA6B,GACtC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}