import axios from 'axios';

export class InstagramUltimateExtractor {
  
  // Método que usa múltiples servicios reales que funcionan
  static async extractWithWorkingServices(url: string) {
    console.log('🔥 MÉTODO DEFINITIVO - Usando servicios que SÍ funcionan...');
    
    const workingServices = [
      {
        name: 'SaveFrom.net',
        extract: () => this.extractFromSaveFrom(url)
      },
      {
        name: 'Y2Mate',
        extract: () => this.extractFromY2Mate(url)
      },
      {
        name: 'SSSTik',
        extract: () => this.extractFromSSSTik(url)
      },
      {
        name: 'SnapTik',
        extract: () => this.extractFromSnapTik(url)
      },
      {
        name: 'InstagramSave',
        extract: () => this.extractFromInstagramSave(url)
      }
    ];

    for (const service of workingServices) {
      try {
        console.log(`🎯 Probando ${service.name}...`);
        const result = await service.extract();
        if (result.success) {
          console.log(`✅ ¡ÉXITO con ${service.name}!`);
          return result;
        }
      } catch (error) {
        console.log(`❌ ${service.name} falló:`, error.message);
        continue;
      }
    }

    return { success: false };
  }

  // SaveFrom.net - Muy confiable
  private static async extractFromSaveFrom(url: string) {
    try {
      const response = await axios.post('https://worker.sf-tools.com/save', {
        url: url
      }, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'application/json',
          'Origin': 'https://savefrom.net',
          'Referer': 'https://savefrom.net/'
        },
        timeout: 20000
      });

      if (response.data && response.data.url && response.data.url.length > 0) {
        const videoData = response.data.url[0];
        return {
          success: true,
          videoUrl: videoData.url,
          thumbnail: response.data.meta?.image,
          title: response.data.meta?.title || 'Video de Instagram'
        };
      }
      return { success: false };
    } catch (error) {
      return { success: false };
    }
  }

  // Y2Mate - Funciona bien
  private static async extractFromY2Mate(url: string) {
    try {
      // Paso 1: Analizar la URL
      const analyzeResponse = await axios.post('https://www.y2mate.com/mates/analyzeV2/ajax', 
        new URLSearchParams({
          k_query: url,
          k_page: 'home',
          hl: 'en',
          q_auto: '0'
        }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': '*/*',
          'X-Requested-With': 'XMLHttpRequest',
          'Origin': 'https://www.y2mate.com',
          'Referer': 'https://www.y2mate.com/'
        },
        timeout: 20000
      });

      if (analyzeResponse.data && analyzeResponse.data.links) {
        const videoLinks = analyzeResponse.data.links.mp4;
        if (videoLinks) {
          const bestQuality = Object.keys(videoLinks)[0];
          const videoData = videoLinks[bestQuality];
          
          // Paso 2: Obtener el enlace de descarga
          const convertResponse = await axios.post('https://www.y2mate.com/mates/convertV2/ajax', 
            new URLSearchParams({
              vid: analyzeResponse.data.vid,
              k: videoData.k
            }), {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
              'Accept': '*/*',
              'X-Requested-With': 'XMLHttpRequest',
              'Origin': 'https://www.y2mate.com',
              'Referer': 'https://www.y2mate.com/'
            },
            timeout: 20000
          });

          if (convertResponse.data && convertResponse.data.dlink) {
            return {
              success: true,
              videoUrl: convertResponse.data.dlink,
              thumbnail: analyzeResponse.data.thumbnail,
              title: analyzeResponse.data.title || 'Video de Instagram'
            };
          }
        }
      }
      return { success: false };
    } catch (error) {
      return { success: false };
    }
  }

  // SSSTik - Especializado en redes sociales
  private static async extractFromSSSTik(url: string) {
    try {
      const response = await axios.post('https://ssstik.io/abc', 
        new URLSearchParams({
          id: url,
          locale: 'en',
          tt: 'RFBiOUE'
        }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15',
          'Accept': '*/*',
          'X-Requested-With': 'XMLHttpRequest',
          'Origin': 'https://ssstik.io',
          'Referer': 'https://ssstik.io/'
        },
        timeout: 20000
      });

      if (response.data) {
        const html = response.data;
        const videoMatch = html.match(/href="([^"]*\.mp4[^"]*)"/);
        const thumbnailMatch = html.match(/src="([^"]*\.(jpg|jpeg|png)[^"]*)"/);
        
        if (videoMatch && videoMatch[1]) {
          return {
            success: true,
            videoUrl: videoMatch[1],
            thumbnail: thumbnailMatch ? thumbnailMatch[1] : null,
            title: 'Video de Instagram'
          };
        }
      }
      return { success: false };
    } catch (error) {
      return { success: false };
    }
  }

  // SnapTik - Muy efectivo
  private static async extractFromSnapTik(url: string) {
    try {
      const response = await axios.post('https://snaptik.app/action', 
        new URLSearchParams({
          url: url
        }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Origin': 'https://snaptik.app',
          'Referer': 'https://snaptik.app/'
        },
        timeout: 20000
      });

      if (response.data) {
        const html = response.data;
        const videoMatch = html.match(/href="([^"]*)" download[^>]*>.*?Download/i);
        const thumbnailMatch = html.match(/<img[^>]*src="([^"]*)"[^>]*>/);
        
        if (videoMatch && videoMatch[1] && !videoMatch[1].includes('javascript:')) {
          return {
            success: true,
            videoUrl: videoMatch[1],
            thumbnail: thumbnailMatch ? thumbnailMatch[1] : null,
            title: 'Video de Instagram'
          };
        }
      }
      return { success: false };
    } catch (error) {
      return { success: false };
    }
  }

  // InstagramSave - Especializado
  private static async extractFromInstagramSave(url: string) {
    try {
      const response = await axios.post('https://instagramsave.net/download.php', 
        new URLSearchParams({
          url: url,
          submit: 'Download'
        }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Origin': 'https://instagramsave.net',
          'Referer': 'https://instagramsave.net/'
        },
        timeout: 20000
      });

      if (response.data) {
        const html = response.data;
        const videoMatch = html.match(/href="([^"]*\.mp4[^"]*)"/);
        const thumbnailMatch = html.match(/src="([^"]*\.(jpg|jpeg|png)[^"]*)"/);
        
        if (videoMatch && videoMatch[1]) {
          return {
            success: true,
            videoUrl: videoMatch[1],
            thumbnail: thumbnailMatch ? thumbnailMatch[1] : null,
            title: 'Video de Instagram'
          };
        }
      }
      return { success: false };
    } catch (error) {
      return { success: false };
    }
  }

  // Método de validación de URLs de video
  static async validateAndGetBestQuality(videoUrl: string) {
    try {
      const response = await axios.head(videoUrl, {
        timeout: 5000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15'
        }
      });
      
      const isValid = response.status === 200 && 
                     response.headers['content-type']?.includes('video');
      
      const contentLength = response.headers['content-length'];
      const fileSize = contentLength ? this.formatFileSize(parseInt(contentLength)) : 'Desconocido';
      
      return {
        isValid,
        fileSize,
        contentType: response.headers['content-type']
      };
    } catch (error) {
      return {
        isValid: false,
        fileSize: 'Desconocido',
        contentType: 'unknown'
      };
    }
  }

  private static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
