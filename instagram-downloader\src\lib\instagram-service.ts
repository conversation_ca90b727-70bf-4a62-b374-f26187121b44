import axios from 'axios';

export interface VideoQuality {
  url: string;
  quality: string;
  width?: number;
  height?: number;
  size?: string;
}

export interface VideoData {
  type: 'video' | 'carousel';
  qualities?: VideoQuality[];
  videos?: { qualities: VideoQuality[]; thumbnail?: string }[];
  thumbnail?: string;
  caption?: string;
}

export class InstagramService {
  private static readonly USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
  
  static async getVideoData(url: string): Promise<VideoData> {
    const shortcode = this.extractShortcode(url);
    if (!shortcode) {
      throw new Error('URL de Instagram no válida');
    }

    // Intentar múltiples métodos de extracción
    const methods = [
      () => this.getDataFromAPI(shortcode),
      () => this.getDataFromHTML(url),
      () => this.getDataFromEmbedAPI(shortcode),
    ];

    for (const method of methods) {
      try {
        const result = await method();
        if (result) {
          return result;
        }
      } catch (error) {
        console.warn('Método falló, intentando siguiente:', error);
        continue;
      }
    }

    throw new Error('No se pudo extraer el video de Instagram');
  }

  private static extractShortcode(url: string): string | null {
    const match = url.match(/\/(p|reel|tv)\/([A-Za-z0-9_-]+)/);
    return match ? match[2] : null;
  }

  private static async getDataFromAPI(shortcode: string): Promise<VideoData | null> {
    try {
      const apiUrl = `https://www.instagram.com/p/${shortcode}/?__a=1&__d=dis`;
      const response = await axios.get(apiUrl, {
        headers: {
          'User-Agent': this.USER_AGENT,
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
        },
        timeout: 10000,
      });

      const data = response.data;
      
      if (data?.items?.[0]) {
        return this.parseMediaItem(data.items[0]);
      }

      return null;
    } catch (error) {
      throw new Error('API method failed');
    }
  }

  private static async getDataFromHTML(url: string): Promise<VideoData | null> {
    try {
      const response = await axios.get(url, {
        headers: {
          'User-Agent': this.USER_AGENT,
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        },
        timeout: 10000,
      });

      const html = response.data;
      
      // Método 1: JSON-LD
      const jsonLdMatch = html.match(/<script type="application\/ld\+json"[^>]*>(.*?)<\/script>/s);
      if (jsonLdMatch) {
        try {
          const jsonData = JSON.parse(jsonLdMatch[1]);
          if (jsonData.video?.contentUrl) {
            return {
              type: 'video',
              qualities: [{
                url: jsonData.video.contentUrl,
                quality: 'Original',
              }],
              thumbnail: jsonData.video.thumbnailUrl,
              caption: jsonData.caption || '',
            };
          }
        } catch (e) {
          // Continuar con otros métodos
        }
      }

      // Método 2: window._sharedData
      const sharedDataMatch = html.match(/window\._sharedData\s*=\s*({.+?});/);
      if (sharedDataMatch) {
        try {
          const sharedData = JSON.parse(sharedDataMatch[1]);
          const media = sharedData?.entry_data?.PostPage?.[0]?.graphql?.shortcode_media;
          
          if (media) {
            return this.parseGraphQLMedia(media);
          }
        } catch (e) {
          // Continuar con otros métodos
        }
      }

      // Método 3: Buscar URLs de video directamente en el HTML
      const videoUrlMatch = html.match(/"video_url":"([^"]+)"/);
      if (videoUrlMatch) {
        const videoUrl = videoUrlMatch[1].replace(/\\u0026/g, '&').replace(/\\/g, '');
        return {
          type: 'video',
          qualities: [{
            url: videoUrl,
            quality: 'Original',
          }],
        };
      }

      return null;
    } catch (error) {
      throw new Error('HTML parsing method failed');
    }
  }

  private static async getDataFromEmbedAPI(shortcode: string): Promise<VideoData | null> {
    try {
      const embedUrl = `https://www.instagram.com/p/${shortcode}/embed/`;
      const response = await axios.get(embedUrl, {
        headers: {
          'User-Agent': this.USER_AGENT,
        },
        timeout: 10000,
      });

      const html = response.data;
      
      // Buscar datos en el HTML del embed
      const videoMatch = html.match(/"video_url":"([^"]+)"/);
      if (videoMatch) {
        const videoUrl = videoMatch[1].replace(/\\u0026/g, '&').replace(/\\/g, '');
        return {
          type: 'video',
          qualities: [{
            url: videoUrl,
            quality: 'Original',
          }],
        };
      }

      return null;
    } catch (error) {
      throw new Error('Embed API method failed');
    }
  }

  private static parseMediaItem(item: any): VideoData | null {
    if (item.video_versions) {
      // Es un video simple
      const qualities = item.video_versions.map((version: any) => ({
        url: version.url,
        quality: `${version.width}x${version.height}`,
        width: version.width,
        height: version.height,
      }));

      qualities.sort((a: any, b: any) => (b.width * b.height) - (a.width * a.height));

      return {
        type: 'video',
        qualities,
        thumbnail: item.image_versions2?.candidates?.[0]?.url,
        caption: item.caption?.text || '',
      };
    } else if (item.carousel_media) {
      // Es un carrusel
      const videos = item.carousel_media
        .filter((media: any) => media.video_versions)
        .map((media: any) => ({
          qualities: media.video_versions.map((version: any) => ({
            url: version.url,
            quality: `${version.width}x${version.height}`,
            width: version.width,
            height: version.height,
          })).sort((a: any, b: any) => (b.width * b.height) - (a.width * a.height)),
          thumbnail: media.image_versions2?.candidates?.[0]?.url,
        }));

      if (videos.length > 0) {
        return {
          type: 'carousel',
          videos,
          caption: item.caption?.text || '',
        };
      }
    }

    return null;
  }

  private static parseGraphQLMedia(media: any): VideoData | null {
    if (media.is_video && media.video_url) {
      return {
        type: 'video',
        qualities: [{
          url: media.video_url,
          quality: 'Original',
        }],
        thumbnail: media.display_url,
        caption: media.edge_media_to_caption?.edges?.[0]?.node?.text || '',
      };
    }

    return null;
  }

  static isValidInstagramUrl(url: string): boolean {
    const instagramRegex = /^https?:\/\/(www\.)?(instagram\.com|instagr\.am)\/(p|reel|tv)\/[A-Za-z0-9_-]+/;
    return instagramRegex.test(url);
  }
}
