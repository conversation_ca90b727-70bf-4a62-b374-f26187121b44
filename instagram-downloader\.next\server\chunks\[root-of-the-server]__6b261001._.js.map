{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/videostiktok/instagram-downloader/src/app/api/test/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function GET() {\n  return NextResponse.json({ message: 'API funcionando correctamente', timestamp: new Date().toISOString() });\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    return NextResponse.json({ \n      message: 'POST recibido correctamente', \n      data: body,\n      timestamp: new Date().toISOString() \n    });\n  } catch (error) {\n    return NextResponse.json({ \n      error: 'Error al procesar POST',\n      timestamp: new Date().toISOString() \n    }, { status: 400 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAAE,SAAS;QAAiC,WAAW,IAAI,OAAO,WAAW;IAAG;AAC3G;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,WAAW,IAAI,OAAO,WAAW;QACnC;IACF,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,WAAW,IAAI,OAAO,WAAW;QACnC,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}