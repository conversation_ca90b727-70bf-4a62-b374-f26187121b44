module.exports = {

"[project]/.next-internal/server/app/api/download/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/lib/real-instagram-downloader.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "RealInstagramDownloader": (()=>RealInstagramDownloader)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
;
class RealInstagramDownloader {
    static CORS_PROXY = 'https://api.allorigins.win/raw?url=';
    static async downloadVideo(url) {
        try {
            // Limpiar la URL
            const cleanUrl = url.split('?')[0];
            // Método 1: Usar servicio público de descarga
            const result1 = await this.tryMethod1(cleanUrl);
            if (result1.success) return result1;
            // Método 2: Usar otro servicio
            const result2 = await this.tryMethod2(cleanUrl);
            if (result2.success) return result2;
            // Método 3: Scraping directo
            const result3 = await this.tryMethod3(cleanUrl);
            if (result3.success) return result3;
            return {
                success: false,
                error: 'No se pudo extraer el video. El contenido podría ser privado o no estar disponible.'
            };
        } catch (error) {
            return {
                success: false,
                error: 'Error al procesar la solicitud.'
            };
        }
    }
    static async tryMethod1(url) {
        try {
            // Usar API de SnapInsta
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].post('https://snapinsta.app/action.php', new URLSearchParams({
                url: url,
                action: 'post'
            }), {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Referer': 'https://snapinsta.app/'
                },
                timeout: 15000
            });
            if (response.data) {
                const html = response.data;
                // Buscar enlaces de descarga
                const videoMatch = html.match(/href="([^"]*)" download[^>]*>.*?(Download|Descargar)/i);
                const thumbnailMatch = html.match(/<img[^>]*src="([^"]*)"[^>]*>/);
                if (videoMatch && videoMatch[1] && !videoMatch[1].includes('javascript:')) {
                    return {
                        success: true,
                        data: {
                            videoUrl: videoMatch[1],
                            thumbnail: thumbnailMatch ? thumbnailMatch[1] : undefined,
                            quality: 'HD'
                        }
                    };
                }
            }
            return {
                success: false,
                error: 'Method 1 failed'
            };
        } catch (error) {
            return {
                success: false,
                error: 'Method 1 failed'
            };
        }
    }
    static async tryMethod2(url) {
        try {
            // Usar API de SaveIG
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].post('https://www.saveig.app/api/ajaxSearch', new URLSearchParams({
                q: url,
                t: 'media',
                lang: 'en'
            }), {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
                    'Accept': '*/*',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Referer': 'https://www.saveig.app/'
                },
                timeout: 15000
            });
            if (response.data && response.data.data) {
                const html = response.data.data;
                // Extraer URL del video
                const videoMatch = html.match(/href="([^"]*\.mp4[^"]*)"/);
                const thumbnailMatch = html.match(/src="([^"]*\.(jpg|jpeg|png)[^"]*)"/);
                if (videoMatch && videoMatch[1]) {
                    return {
                        success: true,
                        data: {
                            videoUrl: videoMatch[1],
                            thumbnail: thumbnailMatch ? thumbnailMatch[1] : undefined,
                            quality: 'HD'
                        }
                    };
                }
            }
            return {
                success: false,
                error: 'Method 2 failed'
            };
        } catch (error) {
            return {
                success: false,
                error: 'Method 2 failed'
            };
        }
    }
    static async tryMethod3(url) {
        try {
            // Scraping directo con proxy CORS
            const proxyUrl = this.CORS_PROXY + encodeURIComponent(url);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(proxyUrl, {
                headers: {
                    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1'
                },
                timeout: 15000
            });
            const html = response.data;
            // Buscar patrones de video en el HTML
            const patterns = [
                /"video_url":"([^"]+)"/,
                /"src":"([^"]*\.mp4[^"]*)"/,
                /videoUrl":"([^"]+)"/,
                /"contentUrl":"([^"]+\.mp4[^"]*)"/,
                /video_url\\?":\\?"([^"]+)\\?"/
            ];
            for (const pattern of patterns){
                const match = html.match(pattern);
                if (match) {
                    const videoUrl = match[1].replace(/\\u0026/g, '&').replace(/\\/g, '');
                    if (videoUrl && videoUrl.includes('.mp4') && !videoUrl.includes('blob:')) {
                        // Buscar thumbnail
                        const thumbnailMatch = html.match(/"display_url":"([^"]+)"/);
                        const thumbnail = thumbnailMatch ? thumbnailMatch[1].replace(/\\/g, '') : undefined;
                        return {
                            success: true,
                            data: {
                                videoUrl: videoUrl,
                                thumbnail,
                                quality: 'Original'
                            }
                        };
                    }
                }
            }
            return {
                success: false,
                error: 'Method 3 failed'
            };
        } catch (error) {
            return {
                success: false,
                error: 'Method 3 failed'
            };
        }
    }
    static isValidInstagramUrl(url) {
        const regex = /^https?:\/\/(www\.)?(instagram\.com|instagr\.am)\/(p|reel|tv)\/[A-Za-z0-9_-]+/;
        return regex.test(url);
    }
}
}}),
"[project]/src/app/api/download/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$real$2d$instagram$2d$downloader$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/real-instagram-downloader.ts [app-route] (ecmascript)");
;
;
async function POST(request) {
    try {
        const { url } = await request.json();
        if (!url) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'URL es requerida'
            }, {
                status: 400
            });
        }
        // Validar que sea una URL de Instagram
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$real$2d$instagram$2d$downloader$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RealInstagramDownloader"].isValidInstagramUrl(url)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'URL de Instagram no válida'
            }, {
                status: 400
            });
        }
        try {
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$real$2d$instagram$2d$downloader$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RealInstagramDownloader"].downloadVideo(url);
            if (result.success && result.data) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    type: 'video',
                    qualities: [
                        {
                            url: result.data.videoUrl,
                            quality: result.data.quality
                        }
                    ],
                    thumbnail: result.data.thumbnail,
                    caption: result.data.title || 'Video de Instagram'
                });
            } else {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: result.error || 'No se pudo extraer el video de Instagram'
                }, {
                    status: 404
                });
            }
        } catch (error) {
            console.error('Error al descargar video:', error);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Error al procesar el video. Inténtalo de nuevo más tarde.'
            }, {
                status: 503
            });
        }
    } catch (error) {
        console.error('Error en la API:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Error interno del servidor'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__305761a9._.js.map