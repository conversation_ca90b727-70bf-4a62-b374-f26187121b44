{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/videostiktok/instagram-downloader/src/lib/instagram-service.ts"], "sourcesContent": ["import axios from 'axios';\n\nexport interface VideoQuality {\n  url: string;\n  quality: string;\n  width?: number;\n  height?: number;\n  size?: string;\n}\n\nexport interface VideoData {\n  type: 'video' | 'carousel';\n  qualities?: VideoQuality[];\n  videos?: { qualities: VideoQuality[]; thumbnail?: string }[];\n  thumbnail?: string;\n  caption?: string;\n}\n\nexport class InstagramService {\n  private static readonly USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';\n  \n  static async getVideoData(url: string): Promise<VideoData> {\n    const shortcode = this.extractShortcode(url);\n    if (!shortcode) {\n      throw new Error('URL de Instagram no válida');\n    }\n\n    // Intentar múltiples métodos de extracción\n    const methods = [\n      () => this.getDataFromAPI(shortcode),\n      () => this.getDataFromHTML(url),\n      () => this.getDataFromEmbedAPI(shortcode),\n    ];\n\n    for (const method of methods) {\n      try {\n        const result = await method();\n        if (result) {\n          return result;\n        }\n      } catch (error) {\n        console.warn('Método falló, intentando siguiente:', error);\n        continue;\n      }\n    }\n\n    throw new Error('No se pudo extraer el video de Instagram');\n  }\n\n  private static extractShortcode(url: string): string | null {\n    const match = url.match(/\\/(p|reel|tv)\\/([A-Za-z0-9_-]+)/);\n    return match ? match[2] : null;\n  }\n\n  private static async getDataFromAPI(shortcode: string): Promise<VideoData | null> {\n    try {\n      const apiUrl = `https://www.instagram.com/p/${shortcode}/?__a=1&__d=dis`;\n      const response = await axios.get(apiUrl, {\n        headers: {\n          'User-Agent': this.USER_AGENT,\n          'Accept': 'application/json',\n          'X-Requested-With': 'XMLHttpRequest',\n        },\n        timeout: 10000,\n      });\n\n      const data = response.data;\n      \n      if (data?.items?.[0]) {\n        return this.parseMediaItem(data.items[0]);\n      }\n\n      return null;\n    } catch (error) {\n      throw new Error('API method failed');\n    }\n  }\n\n  private static async getDataFromHTML(url: string): Promise<VideoData | null> {\n    try {\n      const response = await axios.get(url, {\n        headers: {\n          'User-Agent': this.USER_AGENT,\n          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',\n        },\n        timeout: 10000,\n      });\n\n      const html = response.data;\n      \n      // Método 1: JSON-LD\n      const jsonLdMatch = html.match(/<script type=\"application\\/ld\\+json\"[^>]*>(.*?)<\\/script>/s);\n      if (jsonLdMatch) {\n        try {\n          const jsonData = JSON.parse(jsonLdMatch[1]);\n          if (jsonData.video?.contentUrl) {\n            return {\n              type: 'video',\n              qualities: [{\n                url: jsonData.video.contentUrl,\n                quality: 'Original',\n              }],\n              thumbnail: jsonData.video.thumbnailUrl,\n              caption: jsonData.caption || '',\n            };\n          }\n        } catch (e) {\n          // Continuar con otros métodos\n        }\n      }\n\n      // Método 2: window._sharedData\n      const sharedDataMatch = html.match(/window\\._sharedData\\s*=\\s*({.+?});/);\n      if (sharedDataMatch) {\n        try {\n          const sharedData = JSON.parse(sharedDataMatch[1]);\n          const media = sharedData?.entry_data?.PostPage?.[0]?.graphql?.shortcode_media;\n          \n          if (media) {\n            return this.parseGraphQLMedia(media);\n          }\n        } catch (e) {\n          // Continuar con otros métodos\n        }\n      }\n\n      // Método 3: Buscar URLs de video directamente en el HTML\n      const videoUrlMatch = html.match(/\"video_url\":\"([^\"]+)\"/);\n      if (videoUrlMatch) {\n        const videoUrl = videoUrlMatch[1].replace(/\\\\u0026/g, '&').replace(/\\\\/g, '');\n        return {\n          type: 'video',\n          qualities: [{\n            url: videoUrl,\n            quality: 'Original',\n          }],\n        };\n      }\n\n      return null;\n    } catch (error) {\n      throw new Error('HTML parsing method failed');\n    }\n  }\n\n  private static async getDataFromEmbedAPI(shortcode: string): Promise<VideoData | null> {\n    try {\n      const embedUrl = `https://www.instagram.com/p/${shortcode}/embed/`;\n      const response = await axios.get(embedUrl, {\n        headers: {\n          'User-Agent': this.USER_AGENT,\n        },\n        timeout: 10000,\n      });\n\n      const html = response.data;\n      \n      // Buscar datos en el HTML del embed\n      const videoMatch = html.match(/\"video_url\":\"([^\"]+)\"/);\n      if (videoMatch) {\n        const videoUrl = videoMatch[1].replace(/\\\\u0026/g, '&').replace(/\\\\/g, '');\n        return {\n          type: 'video',\n          qualities: [{\n            url: videoUrl,\n            quality: 'Original',\n          }],\n        };\n      }\n\n      return null;\n    } catch (error) {\n      throw new Error('Embed API method failed');\n    }\n  }\n\n  private static parseMediaItem(item: any): VideoData | null {\n    if (item.video_versions) {\n      // Es un video simple\n      const qualities = item.video_versions.map((version: any) => ({\n        url: version.url,\n        quality: `${version.width}x${version.height}`,\n        width: version.width,\n        height: version.height,\n      }));\n\n      qualities.sort((a: any, b: any) => (b.width * b.height) - (a.width * a.height));\n\n      return {\n        type: 'video',\n        qualities,\n        thumbnail: item.image_versions2?.candidates?.[0]?.url,\n        caption: item.caption?.text || '',\n      };\n    } else if (item.carousel_media) {\n      // Es un carrusel\n      const videos = item.carousel_media\n        .filter((media: any) => media.video_versions)\n        .map((media: any) => ({\n          qualities: media.video_versions.map((version: any) => ({\n            url: version.url,\n            quality: `${version.width}x${version.height}`,\n            width: version.width,\n            height: version.height,\n          })).sort((a: any, b: any) => (b.width * b.height) - (a.width * a.height)),\n          thumbnail: media.image_versions2?.candidates?.[0]?.url,\n        }));\n\n      if (videos.length > 0) {\n        return {\n          type: 'carousel',\n          videos,\n          caption: item.caption?.text || '',\n        };\n      }\n    }\n\n    return null;\n  }\n\n  private static parseGraphQLMedia(media: any): VideoData | null {\n    if (media.is_video && media.video_url) {\n      return {\n        type: 'video',\n        qualities: [{\n          url: media.video_url,\n          quality: 'Original',\n        }],\n        thumbnail: media.display_url,\n        caption: media.edge_media_to_caption?.edges?.[0]?.node?.text || '',\n      };\n    }\n\n    return null;\n  }\n\n  static isValidInstagramUrl(url: string): boolean {\n    const instagramRegex = /^https?:\\/\\/(www\\.)?(instagram\\.com|instagr\\.am)\\/(p|reel|tv)\\/[A-Za-z0-9_-]+/;\n    return instagramRegex.test(url);\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAkBO,MAAM;IACX,OAAwB,aAAa,sHAAsH;IAE3J,aAAa,aAAa,GAAW,EAAsB;QACzD,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;QACxC,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,2CAA2C;QAC3C,MAAM,UAAU;YACd,IAAM,IAAI,CAAC,cAAc,CAAC;YAC1B,IAAM,IAAI,CAAC,eAAe,CAAC;YAC3B,IAAM,IAAI,CAAC,mBAAmB,CAAC;SAChC;QAED,KAAK,MAAM,UAAU,QAAS;YAC5B,IAAI;gBACF,MAAM,SAAS,MAAM;gBACrB,IAAI,QAAQ;oBACV,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,uCAAuC;gBACpD;YACF;QACF;QAEA,MAAM,IAAI,MAAM;IAClB;IAEA,OAAe,iBAAiB,GAAW,EAAiB;QAC1D,MAAM,QAAQ,IAAI,KAAK,CAAC;QACxB,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;IAC5B;IAEA,aAAqB,eAAe,SAAiB,EAA6B;QAChF,IAAI;YACF,MAAM,SAAS,CAAC,4BAA4B,EAAE,UAAU,eAAe,CAAC;YACxE,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,QAAQ;gBACvC,SAAS;oBACP,cAAc,IAAI,CAAC,UAAU;oBAC7B,UAAU;oBACV,oBAAoB;gBACtB;gBACA,SAAS;YACX;YAEA,MAAM,OAAO,SAAS,IAAI;YAE1B,IAAI,MAAM,OAAO,CAAC,EAAE,EAAE;gBACpB,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,KAAK,CAAC,EAAE;YAC1C;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAqB,gBAAgB,GAAW,EAA6B;QAC3E,IAAI;YACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;gBACpC,SAAS;oBACP,cAAc,IAAI,CAAC,UAAU;oBAC7B,UAAU;gBACZ;gBACA,SAAS;YACX;YAEA,MAAM,OAAO,SAAS,IAAI;YAE1B,oBAAoB;YACpB,MAAM,cAAc,KAAK,KAAK,CAAC;YAC/B,IAAI,aAAa;gBACf,IAAI;oBACF,MAAM,WAAW,KAAK,KAAK,CAAC,WAAW,CAAC,EAAE;oBAC1C,IAAI,SAAS,KAAK,EAAE,YAAY;wBAC9B,OAAO;4BACL,MAAM;4BACN,WAAW;gCAAC;oCACV,KAAK,SAAS,KAAK,CAAC,UAAU;oCAC9B,SAAS;gCACX;6BAAE;4BACF,WAAW,SAAS,KAAK,CAAC,YAAY;4BACtC,SAAS,SAAS,OAAO,IAAI;wBAC/B;oBACF;gBACF,EAAE,OAAO,GAAG;gBACV,8BAA8B;gBAChC;YACF;YAEA,+BAA+B;YAC/B,MAAM,kBAAkB,KAAK,KAAK,CAAC;YACnC,IAAI,iBAAiB;gBACnB,IAAI;oBACF,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,CAAC,EAAE;oBAChD,MAAM,QAAQ,YAAY,YAAY,UAAU,CAAC,EAAE,EAAE,SAAS;oBAE9D,IAAI,OAAO;wBACT,OAAO,IAAI,CAAC,iBAAiB,CAAC;oBAChC;gBACF,EAAE,OAAO,GAAG;gBACV,8BAA8B;gBAChC;YACF;YAEA,yDAAyD;YACzD,MAAM,gBAAgB,KAAK,KAAK,CAAC;YACjC,IAAI,eAAe;gBACjB,MAAM,WAAW,aAAa,CAAC,EAAE,CAAC,OAAO,CAAC,YAAY,KAAK,OAAO,CAAC,OAAO;gBAC1E,OAAO;oBACL,MAAM;oBACN,WAAW;wBAAC;4BACV,KAAK;4BACL,SAAS;wBACX;qBAAE;gBACJ;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAqB,oBAAoB,SAAiB,EAA6B;QACrF,IAAI;YACF,MAAM,WAAW,CAAC,4BAA4B,EAAE,UAAU,OAAO,CAAC;YAClE,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,UAAU;gBACzC,SAAS;oBACP,cAAc,IAAI,CAAC,UAAU;gBAC/B;gBACA,SAAS;YACX;YAEA,MAAM,OAAO,SAAS,IAAI;YAE1B,oCAAoC;YACpC,MAAM,aAAa,KAAK,KAAK,CAAC;YAC9B,IAAI,YAAY;gBACd,MAAM,WAAW,UAAU,CAAC,EAAE,CAAC,OAAO,CAAC,YAAY,KAAK,OAAO,CAAC,OAAO;gBACvE,OAAO;oBACL,MAAM;oBACN,WAAW;wBAAC;4BACV,KAAK;4BACL,SAAS;wBACX;qBAAE;gBACJ;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,OAAe,eAAe,IAAS,EAAoB;QACzD,IAAI,KAAK,cAAc,EAAE;YACvB,qBAAqB;YACrB,MAAM,YAAY,KAAK,cAAc,CAAC,GAAG,CAAC,CAAC,UAAiB,CAAC;oBAC3D,KAAK,QAAQ,GAAG;oBAChB,SAAS,GAAG,QAAQ,KAAK,CAAC,CAAC,EAAE,QAAQ,MAAM,EAAE;oBAC7C,OAAO,QAAQ,KAAK;oBACpB,QAAQ,QAAQ,MAAM;gBACxB,CAAC;YAED,UAAU,IAAI,CAAC,CAAC,GAAQ,IAAW,AAAC,EAAE,KAAK,GAAG,EAAE,MAAM,GAAK,EAAE,KAAK,GAAG,EAAE,MAAM;YAE7E,OAAO;gBACL,MAAM;gBACN;gBACA,WAAW,KAAK,eAAe,EAAE,YAAY,CAAC,EAAE,EAAE;gBAClD,SAAS,KAAK,OAAO,EAAE,QAAQ;YACjC;QACF,OAAO,IAAI,KAAK,cAAc,EAAE;YAC9B,iBAAiB;YACjB,MAAM,SAAS,KAAK,cAAc,CAC/B,MAAM,CAAC,CAAC,QAAe,MAAM,cAAc,EAC3C,GAAG,CAAC,CAAC,QAAe,CAAC;oBACpB,WAAW,MAAM,cAAc,CAAC,GAAG,CAAC,CAAC,UAAiB,CAAC;4BACrD,KAAK,QAAQ,GAAG;4BAChB,SAAS,GAAG,QAAQ,KAAK,CAAC,CAAC,EAAE,QAAQ,MAAM,EAAE;4BAC7C,OAAO,QAAQ,KAAK;4BACpB,QAAQ,QAAQ,MAAM;wBACxB,CAAC,GAAG,IAAI,CAAC,CAAC,GAAQ,IAAW,AAAC,EAAE,KAAK,GAAG,EAAE,MAAM,GAAK,EAAE,KAAK,GAAG,EAAE,MAAM;oBACvE,WAAW,MAAM,eAAe,EAAE,YAAY,CAAC,EAAE,EAAE;gBACrD,CAAC;YAEH,IAAI,OAAO,MAAM,GAAG,GAAG;gBACrB,OAAO;oBACL,MAAM;oBACN;oBACA,SAAS,KAAK,OAAO,EAAE,QAAQ;gBACjC;YACF;QACF;QAEA,OAAO;IACT;IAEA,OAAe,kBAAkB,KAAU,EAAoB;QAC7D,IAAI,MAAM,QAAQ,IAAI,MAAM,SAAS,EAAE;YACrC,OAAO;gBACL,MAAM;gBACN,WAAW;oBAAC;wBACV,KAAK,MAAM,SAAS;wBACpB,SAAS;oBACX;iBAAE;gBACF,WAAW,MAAM,WAAW;gBAC5B,SAAS,MAAM,qBAAqB,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,QAAQ;YAClE;QACF;QAEA,OAAO;IACT;IAEA,OAAO,oBAAoB,GAAW,EAAW;QAC/C,MAAM,iBAAiB;QACvB,OAAO,eAAe,IAAI,CAAC;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 376, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/videostiktok/instagram-downloader/src/app/api/download/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { InstagramService } from '@/lib/instagram-service';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { url } = await request.json();\n\n    if (!url) {\n      return NextResponse.json(\n        { error: 'URL es requerida' },\n        { status: 400 }\n      );\n    }\n\n    // Validar que sea una URL de Instagram\n    if (!InstagramService.isValidInstagramUrl(url)) {\n      return NextResponse.json(\n        { error: 'URL de Instagram no válida' },\n        { status: 400 }\n      );\n    }\n\n    try {\n      const videoData = await InstagramService.getVideoData(url);\n      return NextResponse.json(videoData);\n    } catch (error: any) {\n      console.error('Error al obtener datos de Instagram:', error.message);\n\n      return NextResponse.json(\n        { error: error.message || 'No se pudo acceder al contenido de Instagram. El video podría ser privado o no estar disponible.' },\n        { status: 503 }\n      );\n    }\n\n  } catch (error: any) {\n    console.error('Error en la API:', error);\n    return NextResponse.json(\n      { error: 'Error interno del servidor' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,QAAQ,IAAI;QAElC,IAAI,CAAC,KAAK;YACR,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,uCAAuC;QACvC,IAAI,CAAC,oIAAA,CAAA,mBAAgB,CAAC,mBAAmB,CAAC,MAAM;YAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6B,GACtC;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI;YACF,MAAM,YAAY,MAAM,oIAAA,CAAA,mBAAgB,CAAC,YAAY,CAAC;YACtD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC3B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,wCAAwC,MAAM,OAAO;YAEnE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,MAAM,OAAO,IAAI;YAAmG,GAC7H;gBAAE,QAAQ;YAAI;QAElB;IAEF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA6B,GACtC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}