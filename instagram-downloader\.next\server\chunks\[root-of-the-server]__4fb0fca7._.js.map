{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/videostiktok/instagram-downloader/src/lib/working-instagram-downloader.ts"], "sourcesContent": ["import axios from 'axios';\n\nexport interface DownloadResult {\n  success: boolean;\n  data?: {\n    type: 'video';\n    qualities: Array<{\n      url: string;\n      quality: string;\n    }>;\n    thumbnail?: string;\n    caption?: string;\n  };\n  error?: string;\n}\n\nexport class WorkingInstagramDownloader {\n  \n  static async downloadVideo(url: string): Promise<DownloadResult> {\n    const cleanUrl = url.split('?')[0];\n    \n    // Método 1: API de Insta Loader\n    const result1 = await this.tryInstaLoader(cleanUrl);\n    if (result1.success) return result1;\n    \n    // Método 2: API de Y2Mate\n    const result2 = await this.tryY2Mate(cleanUrl);\n    if (result2.success) return result2;\n    \n    // Método 3: API de SSS Instagram\n    const result3 = await this.trySSSInstagram(cleanUrl);\n    if (result3.success) return result3;\n    \n    // Método 4: API de InstaSave\n    const result4 = await this.tryInstaSave(cleanUrl);\n    if (result4.success) return result4;\n    \n    return {\n      success: false,\n      error: 'No se pudo descargar el video. Inténtalo con otra URL.'\n    };\n  }\n  \n  private static async tryInstaLoader(url: string): Promise<DownloadResult> {\n    try {\n      const response = await axios.post('https://instaloader.io/api/post', {\n        url: url\n      }, {\n        headers: {\n          'Content-Type': 'application/json',\n          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n          'Referer': 'https://instaloader.io/',\n          'Origin': 'https://instaloader.io'\n        },\n        timeout: 20000\n      });\n\n      if (response.data && response.data.video_url) {\n        return {\n          success: true,\n          data: {\n            type: 'video',\n            qualities: [{\n              url: response.data.video_url,\n              quality: 'HD'\n            }],\n            thumbnail: response.data.thumbnail_url,\n            caption: response.data.caption || ''\n          }\n        };\n      }\n      return { success: false };\n    } catch (error) {\n      return { success: false };\n    }\n  }\n  \n  private static async tryY2Mate(url: string): Promise<DownloadResult> {\n    try {\n      // Paso 1: Analizar la URL\n      const analyzeResponse = await axios.post('https://www.y2mate.com/mates/analyzeV2/ajax', \n        new URLSearchParams({\n          k_query: url,\n          k_page: 'home',\n          hl: 'en',\n          q_auto: '0'\n        }), {\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded',\n          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n          'Referer': 'https://www.y2mate.com/',\n          'Origin': 'https://www.y2mate.com'\n        },\n        timeout: 20000\n      });\n\n      if (analyzeResponse.data && analyzeResponse.data.links) {\n        const videoLinks = analyzeResponse.data.links.mp4;\n        if (videoLinks) {\n          const qualities = Object.keys(videoLinks).map(quality => ({\n            url: videoLinks[quality].url,\n            quality: quality\n          }));\n          \n          return {\n            success: true,\n            data: {\n              type: 'video',\n              qualities,\n              thumbnail: analyzeResponse.data.thumbnail,\n              caption: analyzeResponse.data.title || ''\n            }\n          };\n        }\n      }\n      return { success: false };\n    } catch (error) {\n      return { success: false };\n    }\n  }\n  \n  private static async trySSSInstagram(url: string): Promise<DownloadResult> {\n    try {\n      const response = await axios.post('https://sssinstagram.com/api/ig/post', {\n        url: url\n      }, {\n        headers: {\n          'Content-Type': 'application/json',\n          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15',\n          'Referer': 'https://sssinstagram.com/',\n          'Origin': 'https://sssinstagram.com'\n        },\n        timeout: 20000\n      });\n\n      if (response.data && response.data.data && response.data.data.length > 0) {\n        const videoData = response.data.data[0];\n        if (videoData.video_url) {\n          return {\n            success: true,\n            data: {\n              type: 'video',\n              qualities: [{\n                url: videoData.video_url,\n                quality: 'HD'\n              }],\n              thumbnail: videoData.thumbnail_url,\n              caption: videoData.caption || ''\n            }\n          };\n        }\n      }\n      return { success: false };\n    } catch (error) {\n      return { success: false };\n    }\n  }\n  \n  private static async tryInstaSave(url: string): Promise<DownloadResult> {\n    try {\n      const response = await axios.post('https://instasave.website/system/action.php', \n        new URLSearchParams({\n          url: url,\n          action: 'post'\n        }), {\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded',\n          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',\n          'Referer': 'https://instasave.website/',\n          'Origin': 'https://instasave.website'\n        },\n        timeout: 20000\n      });\n\n      if (response.data) {\n        const html = response.data;\n        \n        // Buscar enlaces de descarga en el HTML\n        const videoMatch = html.match(/href=\"([^\"]*\\.mp4[^\"]*)\"/);\n        const thumbnailMatch = html.match(/src=\"([^\"]*\\.(jpg|jpeg|png)[^\"]*)\"/);\n        \n        if (videoMatch && videoMatch[1]) {\n          return {\n            success: true,\n            data: {\n              type: 'video',\n              qualities: [{\n                url: videoMatch[1],\n                quality: 'HD'\n              }],\n              thumbnail: thumbnailMatch ? thumbnailMatch[1] : undefined,\n              caption: ''\n            }\n          };\n        }\n      }\n      return { success: false };\n    } catch (error) {\n      return { success: false };\n    }\n  }\n  \n  // Método adicional usando proxy CORS\n  static async tryWithProxy(url: string): Promise<DownloadResult> {\n    try {\n      const proxyUrl = `https://api.allorigins.win/raw?url=${encodeURIComponent(url)}`;\n      \n      const response = await axios.get(proxyUrl, {\n        headers: {\n          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15'\n        },\n        timeout: 20000\n      });\n\n      const html = response.data;\n      \n      // Buscar patrones de video\n      const patterns = [\n        /\"video_url\":\"([^\"]+)\"/,\n        /\"playback_url\":\"([^\"]+)\"/,\n        /\"src\":\"([^\"]*\\.mp4[^\"]*)\"/,\n        /videoUrl\":\"([^\"]+)\"/\n      ];\n\n      for (const pattern of patterns) {\n        const match = html.match(pattern);\n        if (match) {\n          const videoUrl = match[1].replace(/\\\\u0026/g, '&').replace(/\\\\/g, '');\n          \n          if (videoUrl && videoUrl.includes('.mp4')) {\n            const thumbnailMatch = html.match(/\"display_url\":\"([^\"]+)\"/);\n            \n            return {\n              success: true,\n              data: {\n                type: 'video',\n                qualities: [{\n                  url: videoUrl,\n                  quality: 'Original'\n                }],\n                thumbnail: thumbnailMatch ? thumbnailMatch[1].replace(/\\\\/g, '') : undefined,\n                caption: ''\n              }\n            };\n          }\n        }\n      }\n      \n      return { success: false };\n    } catch (error) {\n      return { success: false };\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAgBO,MAAM;IAEX,aAAa,cAAc,GAAW,EAA2B;QAC/D,MAAM,WAAW,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;QAElC,gCAAgC;QAChC,MAAM,UAAU,MAAM,IAAI,CAAC,cAAc,CAAC;QAC1C,IAAI,QAAQ,OAAO,EAAE,OAAO;QAE5B,0BAA0B;QAC1B,MAAM,UAAU,MAAM,IAAI,CAAC,SAAS,CAAC;QACrC,IAAI,QAAQ,OAAO,EAAE,OAAO;QAE5B,iCAAiC;QACjC,MAAM,UAAU,MAAM,IAAI,CAAC,eAAe,CAAC;QAC3C,IAAI,QAAQ,OAAO,EAAE,OAAO;QAE5B,6BAA6B;QAC7B,MAAM,UAAU,MAAM,IAAI,CAAC,YAAY,CAAC;QACxC,IAAI,QAAQ,OAAO,EAAE,OAAO;QAE5B,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,aAAqB,eAAe,GAAW,EAA2B;QACxE,IAAI;YACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,mCAAmC;gBACnE,KAAK;YACP,GAAG;gBACD,SAAS;oBACP,gBAAgB;oBAChB,cAAc;oBACd,WAAW;oBACX,UAAU;gBACZ;gBACA,SAAS;YACX;YAEA,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,SAAS,EAAE;gBAC5C,OAAO;oBACL,SAAS;oBACT,MAAM;wBACJ,MAAM;wBACN,WAAW;4BAAC;gCACV,KAAK,SAAS,IAAI,CAAC,SAAS;gCAC5B,SAAS;4BACX;yBAAE;wBACF,WAAW,SAAS,IAAI,CAAC,aAAa;wBACtC,SAAS,SAAS,IAAI,CAAC,OAAO,IAAI;oBACpC;gBACF;YACF;YACA,OAAO;gBAAE,SAAS;YAAM;QAC1B,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,SAAS;YAAM;QAC1B;IACF;IAEA,aAAqB,UAAU,GAAW,EAA2B;QACnE,IAAI;YACF,0BAA0B;YAC1B,MAAM,kBAAkB,MAAM,uIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,+CACvC,IAAI,gBAAgB;gBAClB,SAAS;gBACT,QAAQ;gBACR,IAAI;gBACJ,QAAQ;YACV,IAAI;gBACJ,SAAS;oBACP,gBAAgB;oBAChB,cAAc;oBACd,WAAW;oBACX,UAAU;gBACZ;gBACA,SAAS;YACX;YAEA,IAAI,gBAAgB,IAAI,IAAI,gBAAgB,IAAI,CAAC,KAAK,EAAE;gBACtD,MAAM,aAAa,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;gBACjD,IAAI,YAAY;oBACd,MAAM,YAAY,OAAO,IAAI,CAAC,YAAY,GAAG,CAAC,CAAA,UAAW,CAAC;4BACxD,KAAK,UAAU,CAAC,QAAQ,CAAC,GAAG;4BAC5B,SAAS;wBACX,CAAC;oBAED,OAAO;wBACL,SAAS;wBACT,MAAM;4BACJ,MAAM;4BACN;4BACA,WAAW,gBAAgB,IAAI,CAAC,SAAS;4BACzC,SAAS,gBAAgB,IAAI,CAAC,KAAK,IAAI;wBACzC;oBACF;gBACF;YACF;YACA,OAAO;gBAAE,SAAS;YAAM;QAC1B,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,SAAS;YAAM;QAC1B;IACF;IAEA,aAAqB,gBAAgB,GAAW,EAA2B;QACzE,IAAI;YACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,wCAAwC;gBACxE,KAAK;YACP,GAAG;gBACD,SAAS;oBACP,gBAAgB;oBAChB,cAAc;oBACd,WAAW;oBACX,UAAU;gBACZ;gBACA,SAAS;YACX;YAEA,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG;gBACxE,MAAM,YAAY,SAAS,IAAI,CAAC,IAAI,CAAC,EAAE;gBACvC,IAAI,UAAU,SAAS,EAAE;oBACvB,OAAO;wBACL,SAAS;wBACT,MAAM;4BACJ,MAAM;4BACN,WAAW;gCAAC;oCACV,KAAK,UAAU,SAAS;oCACxB,SAAS;gCACX;6BAAE;4BACF,WAAW,UAAU,aAAa;4BAClC,SAAS,UAAU,OAAO,IAAI;wBAChC;oBACF;gBACF;YACF;YACA,OAAO;gBAAE,SAAS;YAAM;QAC1B,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,SAAS;YAAM;QAC1B;IACF;IAEA,aAAqB,aAAa,GAAW,EAA2B;QACtE,IAAI;YACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,+CAChC,IAAI,gBAAgB;gBAClB,KAAK;gBACL,QAAQ;YACV,IAAI;gBACJ,SAAS;oBACP,gBAAgB;oBAChB,cAAc;oBACd,WAAW;oBACX,UAAU;gBACZ;gBACA,SAAS;YACX;YAEA,IAAI,SAAS,IAAI,EAAE;gBACjB,MAAM,OAAO,SAAS,IAAI;gBAE1B,wCAAwC;gBACxC,MAAM,aAAa,KAAK,KAAK,CAAC;gBAC9B,MAAM,iBAAiB,KAAK,KAAK,CAAC;gBAElC,IAAI,cAAc,UAAU,CAAC,EAAE,EAAE;oBAC/B,OAAO;wBACL,SAAS;wBACT,MAAM;4BACJ,MAAM;4BACN,WAAW;gCAAC;oCACV,KAAK,UAAU,CAAC,EAAE;oCAClB,SAAS;gCACX;6BAAE;4BACF,WAAW,iBAAiB,cAAc,CAAC,EAAE,GAAG;4BAChD,SAAS;wBACX;oBACF;gBACF;YACF;YACA,OAAO;gBAAE,SAAS;YAAM;QAC1B,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,SAAS;YAAM;QAC1B;IACF;IAEA,qCAAqC;IACrC,aAAa,aAAa,GAAW,EAA2B;QAC9D,IAAI;YACF,MAAM,WAAW,CAAC,mCAAmC,EAAE,mBAAmB,MAAM;YAEhF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,UAAU;gBACzC,SAAS;oBACP,cAAc;gBAChB;gBACA,SAAS;YACX;YAEA,MAAM,OAAO,SAAS,IAAI;YAE1B,2BAA2B;YAC3B,MAAM,WAAW;gBACf;gBACA;gBACA;gBACA;aACD;YAED,KAAK,MAAM,WAAW,SAAU;gBAC9B,MAAM,QAAQ,KAAK,KAAK,CAAC;gBACzB,IAAI,OAAO;oBACT,MAAM,WAAW,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,YAAY,KAAK,OAAO,CAAC,OAAO;oBAElE,IAAI,YAAY,SAAS,QAAQ,CAAC,SAAS;wBACzC,MAAM,iBAAiB,KAAK,KAAK,CAAC;wBAElC,OAAO;4BACL,SAAS;4BACT,MAAM;gCACJ,MAAM;gCACN,WAAW;oCAAC;wCACV,KAAK;wCACL,SAAS;oCACX;iCAAE;gCACF,WAAW,iBAAiB,cAAc,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,MAAM;gCACnE,SAAS;4BACX;wBACF;oBACF;gBACF;YACF;YAEA,OAAO;gBAAE,SAAS;YAAM;QAC1B,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,SAAS;YAAM;QAC1B;IACF;AACF", "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/videostiktok/instagram-downloader/src/app/api/download/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { WorkingInstagramDownloader } from '@/lib/working-instagram-downloader';\nimport axios from 'axios';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { url } = await request.json();\n\n    if (!url) {\n      return NextResponse.json(\n        { error: 'URL es requerida' },\n        { status: 400 }\n      );\n    }\n\n    // Validar que sea una URL de Instagram\n    const instagramRegex = /^https?:\\/\\/(www\\.)?(instagram\\.com|instagr\\.am)\\/(p|reel|tv)\\/[A-Za-z0-9_-]+/;\n    if (!instagramRegex.test(url)) {\n      return NextResponse.json(\n        { error: 'URL de Instagram no válida' },\n        { status: 400 }\n      );\n    }\n\n    // Limpiar la URL\n    const cleanUrl = url.split('?')[0];\n\n    try {\n      // Método principal: Usar el downloader que funciona\n      const result = await WorkingInstagramDownloader.downloadVideo(cleanUrl);\n      if (result.success && result.data) {\n        return NextResponse.json(result.data);\n      }\n\n      // Método de respaldo: Usar proxy\n      const proxyResult = await WorkingInstagramDownloader.tryWithProxy(cleanUrl);\n      if (proxyResult.success && proxyResult.data) {\n        return NextResponse.json(proxyResult.data);\n      }\n\n      // Método adicional: API de SaveIG (mejorada)\n      const saveIgResult = await tryImprovedSaveIG(cleanUrl);\n      if (saveIgResult.success) {\n        return NextResponse.json(saveIgResult.data);\n      }\n\n      // Método final: Scraping directo mejorado\n      const scrapingResult = await tryImprovedScraping(cleanUrl);\n      if (scrapingResult.success) {\n        return NextResponse.json(scrapingResult.data);\n      }\n\n      return NextResponse.json(\n        { error: 'No se pudo extraer el video. El contenido podría ser privado o no estar disponible.' },\n        { status: 404 }\n      );\n\n    } catch (error: any) {\n      console.error('Error al procesar video:', error);\n      return NextResponse.json(\n        { error: 'Error al procesar el video. Inténtalo de nuevo.' },\n        { status: 503 }\n      );\n    }\n\n  } catch (error: any) {\n    console.error('Error en la API:', error);\n    return NextResponse.json(\n      { error: 'Error interno del servidor' },\n      { status: 500 }\n    );\n  }\n}\n\n// Método mejorado de SaveIG\nasync function tryImprovedSaveIG(url: string) {\n  try {\n    // Primero obtener la página para conseguir tokens\n    const pageResponse = await axios.get('https://www.saveig.app/', {\n      headers: {\n        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'\n      }\n    });\n\n    const response = await axios.post('https://www.saveig.app/api/ajaxSearch',\n      new URLSearchParams({\n        q: url,\n        t: 'media',\n        lang: 'en'\n      }), {\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded',\n        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',\n        'Accept': 'application/json, text/javascript, */*; q=0.01',\n        'X-Requested-With': 'XMLHttpRequest',\n        'Referer': 'https://www.saveig.app/',\n        'Origin': 'https://www.saveig.app',\n        'Accept-Language': 'en-US,en;q=0.9',\n        'Accept-Encoding': 'gzip, deflate, br',\n        'Connection': 'keep-alive',\n        'Sec-Fetch-Dest': 'empty',\n        'Sec-Fetch-Mode': 'cors',\n        'Sec-Fetch-Site': 'same-origin'\n      },\n      timeout: 20000,\n    });\n\n    if (response.data && response.data.data) {\n      const html = response.data.data;\n\n      // Buscar múltiples patrones de enlaces de descarga\n      const patterns = [\n        /href=\"([^\"]*\\.mp4[^\"]*)\"/g,\n        /data-href=\"([^\"]*\\.mp4[^\"]*)\"/g,\n        /src=\"([^\"]*\\.mp4[^\"]*)\"/g\n      ];\n\n      const videoUrls = [];\n      for (const pattern of patterns) {\n        const matches = [...html.matchAll(pattern)];\n        for (const match of matches) {\n          if (match[1] && match[1].includes('.mp4') && !match[1].includes('javascript:')) {\n            videoUrls.push(match[1]);\n          }\n        }\n      }\n\n      const thumbnailMatch = html.match(/src=\"([^\"]*\\.(jpg|jpeg|png)[^\"]*)\"/);\n\n      if (videoUrls.length > 0) {\n        const qualities = videoUrls.map((url, index) => ({\n          url: url,\n          quality: index === 0 ? 'HD' : 'SD'\n        }));\n\n        return {\n          success: true,\n          data: {\n            type: 'video',\n            qualities,\n            thumbnail: thumbnailMatch ? thumbnailMatch[1] : null,\n            caption: ''\n          }\n        };\n      }\n    }\n    return { success: false };\n  } catch (error) {\n    console.error('SaveIG error:', error);\n    return { success: false };\n  }\n}\n\n// Método de scraping mejorado\nasync function tryImprovedScraping(url: string) {\n  try {\n    const userAgents = [\n      'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',\n      'Mozilla/5.0 (Android 11; Mobile; rv:68.0) Gecko/68.0 Firefox/88.0',\n      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',\n      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15'\n    ];\n\n    for (const userAgent of userAgents) {\n      try {\n        const response = await axios.get(url, {\n          headers: {\n            'User-Agent': userAgent,\n            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',\n            'Accept-Language': 'en-US,en;q=0.9',\n            'Accept-Encoding': 'gzip, deflate, br',\n            'Connection': 'keep-alive',\n            'Upgrade-Insecure-Requests': '1',\n            'Sec-Fetch-Dest': 'document',\n            'Sec-Fetch-Mode': 'navigate',\n            'Sec-Fetch-Site': 'none',\n            'Cache-Control': 'max-age=0'\n          },\n          timeout: 20000,\n        });\n\n        const html = response.data;\n\n        // Patrones mejorados para encontrar videos\n        const patterns = [\n          /\"video_url\":\"([^\"]+)\"/g,\n          /\"playback_url\":\"([^\"]+)\"/g,\n          /\"src\":\"([^\"]*\\.mp4[^\"]*)\"/g,\n          /videoUrl\":\"([^\"]+)\"/g,\n          /\"contentUrl\":\"([^\"]+\\.mp4[^\"]*)\"/g,\n          /video_url\\\\?\":\\\\?\"([^\"]+)\\\\?\"/g,\n          /\"video_versions\":\\[{\"url\":\"([^\"]+)\"/g,\n          /\"dash_manifest\":\"([^\"]+)\"/g\n        ];\n\n        for (const pattern of patterns) {\n          const matches = [...html.matchAll(pattern)];\n          for (const match of matches) {\n            if (match[1]) {\n              let videoUrl = match[1].replace(/\\\\u0026/g, '&').replace(/\\\\/g, '');\n\n              if (videoUrl && (videoUrl.includes('.mp4') || videoUrl.includes('video')) &&\n                  !videoUrl.includes('blob:') && !videoUrl.includes('javascript:')) {\n\n                // Buscar thumbnail\n                const thumbnailPatterns = [\n                  /\"display_url\":\"([^\"]+)\"/,\n                  /\"thumbnail_url\":\"([^\"]+)\"/,\n                  /\"image_versions2\":{\"candidates\":\\[{\"url\":\"([^\"]+)\"/\n                ];\n\n                let thumbnail = null;\n                for (const thumbPattern of thumbnailPatterns) {\n                  const thumbMatch = html.match(thumbPattern);\n                  if (thumbMatch) {\n                    thumbnail = thumbMatch[1].replace(/\\\\/g, '');\n                    break;\n                  }\n                }\n\n                return {\n                  success: true,\n                  data: {\n                    type: 'video',\n                    qualities: [{\n                      url: videoUrl,\n                      quality: 'Original',\n                    }],\n                    thumbnail,\n                    caption: '',\n                  }\n                };\n              }\n            }\n          }\n        }\n      } catch (scrapingError) {\n        console.warn(`Scraping with ${userAgent} failed:`, scrapingError);\n        continue;\n      }\n    }\n    return { success: false };\n  } catch (error) {\n    console.error('Improved scraping error:', error);\n    return { success: false };\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,QAAQ,IAAI;QAElC,IAAI,CAAC,KAAK;YACR,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,uCAAuC;QACvC,MAAM,iBAAiB;QACvB,IAAI,CAAC,eAAe,IAAI,CAAC,MAAM;YAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6B,GACtC;gBAAE,QAAQ;YAAI;QAElB;QAEA,iBAAiB;QACjB,MAAM,WAAW,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;QAElC,IAAI;YACF,oDAAoD;YACpD,MAAM,SAAS,MAAM,kJAAA,CAAA,6BAA0B,CAAC,aAAa,CAAC;YAC9D,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,OAAO,IAAI;YACtC;YAEA,iCAAiC;YACjC,MAAM,cAAc,MAAM,kJAAA,CAAA,6BAA0B,CAAC,YAAY,CAAC;YAClE,IAAI,YAAY,OAAO,IAAI,YAAY,IAAI,EAAE;gBAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,YAAY,IAAI;YAC3C;YAEA,6CAA6C;YAC7C,MAAM,eAAe,MAAM,kBAAkB;YAC7C,IAAI,aAAa,OAAO,EAAE;gBACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,aAAa,IAAI;YAC5C;YAEA,0CAA0C;YAC1C,MAAM,iBAAiB,MAAM,oBAAoB;YACjD,IAAI,eAAe,OAAO,EAAE;gBAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,eAAe,IAAI;YAC9C;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsF,GAC/F;gBAAE,QAAQ;YAAI;QAGlB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkD,GAC3D;gBAAE,QAAQ;YAAI;QAElB;IAEF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA6B,GACtC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,4BAA4B;AAC5B,eAAe,kBAAkB,GAAW;IAC1C,IAAI;QACF,kDAAkD;QAClD,MAAM,eAAe,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,2BAA2B;YAC9D,SAAS;gBACP,cAAc;YAChB;QACF;QAEA,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,yCAChC,IAAI,gBAAgB;YAClB,GAAG;YACH,GAAG;YACH,MAAM;QACR,IAAI;YACJ,SAAS;gBACP,gBAAgB;gBAChB,cAAc;gBACd,UAAU;gBACV,oBAAoB;gBACpB,WAAW;gBACX,UAAU;gBACV,mBAAmB;gBACnB,mBAAmB;gBACnB,cAAc;gBACd,kBAAkB;gBAClB,kBAAkB;gBAClB,kBAAkB;YACpB;YACA,SAAS;QACX;QAEA,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;YACvC,MAAM,OAAO,SAAS,IAAI,CAAC,IAAI;YAE/B,mDAAmD;YACnD,MAAM,WAAW;gBACf;gBACA;gBACA;aACD;YAED,MAAM,YAAY,EAAE;YACpB,KAAK,MAAM,WAAW,SAAU;gBAC9B,MAAM,UAAU;uBAAI,KAAK,QAAQ,CAAC;iBAAS;gBAC3C,KAAK,MAAM,SAAS,QAAS;oBAC3B,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,gBAAgB;wBAC9E,UAAU,IAAI,CAAC,KAAK,CAAC,EAAE;oBACzB;gBACF;YACF;YAEA,MAAM,iBAAiB,KAAK,KAAK,CAAC;YAElC,IAAI,UAAU,MAAM,GAAG,GAAG;gBACxB,MAAM,YAAY,UAAU,GAAG,CAAC,CAAC,KAAK,QAAU,CAAC;wBAC/C,KAAK;wBACL,SAAS,UAAU,IAAI,OAAO;oBAChC,CAAC;gBAED,OAAO;oBACL,SAAS;oBACT,MAAM;wBACJ,MAAM;wBACN;wBACA,WAAW,iBAAiB,cAAc,CAAC,EAAE,GAAG;wBAChD,SAAS;oBACX;gBACF;YACF;QACF;QACA,OAAO;YAAE,SAAS;QAAM;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO;YAAE,SAAS;QAAM;IAC1B;AACF;AAEA,8BAA8B;AAC9B,eAAe,oBAAoB,GAAW;IAC5C,IAAI;QACF,MAAM,aAAa;YACjB;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,aAAa,WAAY;YAClC,IAAI;gBACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;oBACpC,SAAS;wBACP,cAAc;wBACd,UAAU;wBACV,mBAAmB;wBACnB,mBAAmB;wBACnB,cAAc;wBACd,6BAA6B;wBAC7B,kBAAkB;wBAClB,kBAAkB;wBAClB,kBAAkB;wBAClB,iBAAiB;oBACnB;oBACA,SAAS;gBACX;gBAEA,MAAM,OAAO,SAAS,IAAI;gBAE1B,2CAA2C;gBAC3C,MAAM,WAAW;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBAED,KAAK,MAAM,WAAW,SAAU;oBAC9B,MAAM,UAAU;2BAAI,KAAK,QAAQ,CAAC;qBAAS;oBAC3C,KAAK,MAAM,SAAS,QAAS;wBAC3B,IAAI,KAAK,CAAC,EAAE,EAAE;4BACZ,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,YAAY,KAAK,OAAO,CAAC,OAAO;4BAEhE,IAAI,YAAY,CAAC,SAAS,QAAQ,CAAC,WAAW,SAAS,QAAQ,CAAC,QAAQ,KACpE,CAAC,SAAS,QAAQ,CAAC,YAAY,CAAC,SAAS,QAAQ,CAAC,gBAAgB;gCAEpE,mBAAmB;gCACnB,MAAM,oBAAoB;oCACxB;oCACA;oCACA;iCACD;gCAED,IAAI,YAAY;gCAChB,KAAK,MAAM,gBAAgB,kBAAmB;oCAC5C,MAAM,aAAa,KAAK,KAAK,CAAC;oCAC9B,IAAI,YAAY;wCACd,YAAY,UAAU,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO;wCACzC;oCACF;gCACF;gCAEA,OAAO;oCACL,SAAS;oCACT,MAAM;wCACJ,MAAM;wCACN,WAAW;4CAAC;gDACV,KAAK;gDACL,SAAS;4CACX;yCAAE;wCACF;wCACA,SAAS;oCACX;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,CAAC,cAAc,EAAE,UAAU,QAAQ,CAAC,EAAE;gBACnD;YACF;QACF;QACA,OAAO;YAAE,SAAS;QAAM;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;YAAE,SAAS;QAAM;IAC1B;AACF", "debugId": null}}]}