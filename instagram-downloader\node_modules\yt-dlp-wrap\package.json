{"name": "yt-dlp-wrap", "version": "2.3.12", "description": "A simple node.js wrapper for yt-dlp.", "files": ["dist"], "main": "dist/index.js", "scripts": {"prepare": "tsc", "build": "tsc", "test": "mocha --require ts-node/register --timeout 300000 test/**/*.test.ts", "testDebug": "mocha --require ts-node/register --inspect-brk --timeout 300000 test/**/*.test.ts"}, "author": "foxes do code", "repository": {"type": "git", "url": "https://github.com/foxesdocode/yt-dlp-wrap.git"}, "keywords": ["youtube-dl", "youtube-dl-wrapper", "youtube-downloader", "nodejs", "yt-dlp", "yt-dlp-wrapper"], "license": "MIT", "devDependencies": {"@types/mocha": "9.1.0", "@types/node": "17.0.10", "mocha": "9.2.0", "ts-node": "10.4.0", "typescript": "4.5.4"}}