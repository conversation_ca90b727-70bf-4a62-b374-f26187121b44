{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/videostiktok/instagram-downloader/src/lib/real-instagram-downloader.ts"], "sourcesContent": ["import axios from 'axios';\n\nexport interface VideoResult {\n  success: boolean;\n  data?: {\n    videoUrl: string;\n    thumbnail?: string;\n    title?: string;\n    quality: string;\n  };\n  error?: string;\n}\n\nexport class RealInstagramDownloader {\n  private static readonly CORS_PROXY = 'https://api.allorigins.win/raw?url=';\n  \n  static async downloadVideo(url: string): Promise<VideoResult> {\n    try {\n      // Limpiar la URL\n      const cleanUrl = url.split('?')[0];\n      \n      // Método 1: Usar servicio público de descarga\n      const result1 = await this.tryMethod1(cleanUrl);\n      if (result1.success) return result1;\n      \n      // Método 2: Usar otro servicio\n      const result2 = await this.tryMethod2(cleanUrl);\n      if (result2.success) return result2;\n      \n      // Método 3: Scraping directo\n      const result3 = await this.tryMethod3(cleanUrl);\n      if (result3.success) return result3;\n      \n      return {\n        success: false,\n        error: 'No se pudo extraer el video. El contenido podría ser privado o no estar disponible.'\n      };\n      \n    } catch (error) {\n      return {\n        success: false,\n        error: 'Error al procesar la solicitud.'\n      };\n    }\n  }\n  \n  private static async tryMethod1(url: string): Promise<VideoResult> {\n    try {\n      // Usar API de SnapInsta\n      const response = await axios.post('https://snapinsta.app/action.php', \n        new URLSearchParams({\n          url: url,\n          action: 'post'\n        }), {\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded',\n          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',\n          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',\n          'Referer': 'https://snapinsta.app/',\n        },\n        timeout: 15000,\n      });\n\n      if (response.data) {\n        const html = response.data;\n        \n        // Buscar enlaces de descarga\n        const videoMatch = html.match(/href=\"([^\"]*)\" download[^>]*>.*?(Download|Descargar)/i);\n        const thumbnailMatch = html.match(/<img[^>]*src=\"([^\"]*)\"[^>]*>/);\n        \n        if (videoMatch && videoMatch[1] && !videoMatch[1].includes('javascript:')) {\n          return {\n            success: true,\n            data: {\n              videoUrl: videoMatch[1],\n              thumbnail: thumbnailMatch ? thumbnailMatch[1] : undefined,\n              quality: 'HD'\n            }\n          };\n        }\n      }\n\n      return { success: false, error: 'Method 1 failed' };\n    } catch (error) {\n      return { success: false, error: 'Method 1 failed' };\n    }\n  }\n  \n  private static async tryMethod2(url: string): Promise<VideoResult> {\n    try {\n      // Usar API de SaveIG\n      const response = await axios.post('https://www.saveig.app/api/ajaxSearch', \n        new URLSearchParams({\n          q: url,\n          t: 'media',\n          lang: 'en'\n        }), {\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded',\n          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',\n          'Accept': '*/*',\n          'X-Requested-With': 'XMLHttpRequest',\n          'Referer': 'https://www.saveig.app/',\n        },\n        timeout: 15000,\n      });\n\n      if (response.data && response.data.data) {\n        const html = response.data.data;\n        \n        // Extraer URL del video\n        const videoMatch = html.match(/href=\"([^\"]*\\.mp4[^\"]*)\"/);\n        const thumbnailMatch = html.match(/src=\"([^\"]*\\.(jpg|jpeg|png)[^\"]*)\"/);\n        \n        if (videoMatch && videoMatch[1]) {\n          return {\n            success: true,\n            data: {\n              videoUrl: videoMatch[1],\n              thumbnail: thumbnailMatch ? thumbnailMatch[1] : undefined,\n              quality: 'HD'\n            }\n          };\n        }\n      }\n\n      return { success: false, error: 'Method 2 failed' };\n    } catch (error) {\n      return { success: false, error: 'Method 2 failed' };\n    }\n  }\n  \n  private static async tryMethod3(url: string): Promise<VideoResult> {\n    try {\n      // Scraping directo con proxy CORS\n      const proxyUrl = this.CORS_PROXY + encodeURIComponent(url);\n      \n      const response = await axios.get(proxyUrl, {\n        headers: {\n          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',\n        },\n        timeout: 15000,\n      });\n\n      const html = response.data;\n      \n      // Buscar patrones de video en el HTML\n      const patterns = [\n        /\"video_url\":\"([^\"]+)\"/,\n        /\"src\":\"([^\"]*\\.mp4[^\"]*)\"/,\n        /videoUrl\":\"([^\"]+)\"/,\n        /\"contentUrl\":\"([^\"]+\\.mp4[^\"]*)\"/,\n        /video_url\\\\?\":\\\\?\"([^\"]+)\\\\?\"/,\n      ];\n\n      for (const pattern of patterns) {\n        const match = html.match(pattern);\n        if (match) {\n          const videoUrl = match[1].replace(/\\\\u0026/g, '&').replace(/\\\\/g, '');\n          \n          if (videoUrl && videoUrl.includes('.mp4') && !videoUrl.includes('blob:')) {\n            // Buscar thumbnail\n            const thumbnailMatch = html.match(/\"display_url\":\"([^\"]+)\"/);\n            const thumbnail = thumbnailMatch ? thumbnailMatch[1].replace(/\\\\/g, '') : undefined;\n            \n            return {\n              success: true,\n              data: {\n                videoUrl: videoUrl,\n                thumbnail,\n                quality: 'Original'\n              }\n            };\n          }\n        }\n      }\n\n      return { success: false, error: 'Method 3 failed' };\n    } catch (error) {\n      return { success: false, error: 'Method 3 failed' };\n    }\n  }\n  \n  static isValidInstagramUrl(url: string): boolean {\n    const regex = /^https?:\\/\\/(www\\.)?(instagram\\.com|instagr\\.am)\\/(p|reel|tv)\\/[A-Za-z0-9_-]+/;\n    return regex.test(url);\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAaO,MAAM;IACX,OAAwB,aAAa,sCAAsC;IAE3E,aAAa,cAAc,GAAW,EAAwB;QAC5D,IAAI;YACF,iBAAiB;YACjB,MAAM,WAAW,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;YAElC,8CAA8C;YAC9C,MAAM,UAAU,MAAM,IAAI,CAAC,UAAU,CAAC;YACtC,IAAI,QAAQ,OAAO,EAAE,OAAO;YAE5B,+BAA+B;YAC/B,MAAM,UAAU,MAAM,IAAI,CAAC,UAAU,CAAC;YACtC,IAAI,QAAQ,OAAO,EAAE,OAAO;YAE5B,6BAA6B;YAC7B,MAAM,UAAU,MAAM,IAAI,CAAC,UAAU,CAAC;YACtC,IAAI,QAAQ,OAAO,EAAE,OAAO;YAE5B,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QAEF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;IACF;IAEA,aAAqB,WAAW,GAAW,EAAwB;QACjE,IAAI;YACF,wBAAwB;YACxB,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,oCAChC,IAAI,gBAAgB;gBAClB,KAAK;gBACL,QAAQ;YACV,IAAI;gBACJ,SAAS;oBACP,gBAAgB;oBAChB,cAAc;oBACd,UAAU;oBACV,WAAW;gBACb;gBACA,SAAS;YACX;YAEA,IAAI,SAAS,IAAI,EAAE;gBACjB,MAAM,OAAO,SAAS,IAAI;gBAE1B,6BAA6B;gBAC7B,MAAM,aAAa,KAAK,KAAK,CAAC;gBAC9B,MAAM,iBAAiB,KAAK,KAAK,CAAC;gBAElC,IAAI,cAAc,UAAU,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,QAAQ,CAAC,gBAAgB;oBACzE,OAAO;wBACL,SAAS;wBACT,MAAM;4BACJ,UAAU,UAAU,CAAC,EAAE;4BACvB,WAAW,iBAAiB,cAAc,CAAC,EAAE,GAAG;4BAChD,SAAS;wBACX;oBACF;gBACF;YACF;YAEA,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAkB;QACpD,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAkB;QACpD;IACF;IAEA,aAAqB,WAAW,GAAW,EAAwB;QACjE,IAAI;YACF,qBAAqB;YACrB,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,yCAChC,IAAI,gBAAgB;gBAClB,GAAG;gBACH,GAAG;gBACH,MAAM;YACR,IAAI;gBACJ,SAAS;oBACP,gBAAgB;oBAChB,cAAc;oBACd,UAAU;oBACV,oBAAoB;oBACpB,WAAW;gBACb;gBACA,SAAS;YACX;YAEA,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBACvC,MAAM,OAAO,SAAS,IAAI,CAAC,IAAI;gBAE/B,wBAAwB;gBACxB,MAAM,aAAa,KAAK,KAAK,CAAC;gBAC9B,MAAM,iBAAiB,KAAK,KAAK,CAAC;gBAElC,IAAI,cAAc,UAAU,CAAC,EAAE,EAAE;oBAC/B,OAAO;wBACL,SAAS;wBACT,MAAM;4BACJ,UAAU,UAAU,CAAC,EAAE;4BACvB,WAAW,iBAAiB,cAAc,CAAC,EAAE,GAAG;4BAChD,SAAS;wBACX;oBACF;gBACF;YACF;YAEA,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAkB;QACpD,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAkB;QACpD;IACF;IAEA,aAAqB,WAAW,GAAW,EAAwB;QACjE,IAAI;YACF,kCAAkC;YAClC,MAAM,WAAW,IAAI,CAAC,UAAU,GAAG,mBAAmB;YAEtD,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,UAAU;gBACzC,SAAS;oBACP,cAAc;gBAChB;gBACA,SAAS;YACX;YAEA,MAAM,OAAO,SAAS,IAAI;YAE1B,sCAAsC;YACtC,MAAM,WAAW;gBACf;gBACA;gBACA;gBACA;gBACA;aACD;YAED,KAAK,MAAM,WAAW,SAAU;gBAC9B,MAAM,QAAQ,KAAK,KAAK,CAAC;gBACzB,IAAI,OAAO;oBACT,MAAM,WAAW,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,YAAY,KAAK,OAAO,CAAC,OAAO;oBAElE,IAAI,YAAY,SAAS,QAAQ,CAAC,WAAW,CAAC,SAAS,QAAQ,CAAC,UAAU;wBACxE,mBAAmB;wBACnB,MAAM,iBAAiB,KAAK,KAAK,CAAC;wBAClC,MAAM,YAAY,iBAAiB,cAAc,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,MAAM;wBAE1E,OAAO;4BACL,SAAS;4BACT,MAAM;gCACJ,UAAU;gCACV;gCACA,SAAS;4BACX;wBACF;oBACF;gBACF;YACF;YAEA,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAkB;QACpD,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAkB;QACpD;IACF;IAEA,OAAO,oBAAoB,GAAW,EAAW;QAC/C,MAAM,QAAQ;QACd,OAAO,MAAM,IAAI,CAAC;IACpB;AACF", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/videostiktok/instagram-downloader/src/app/api/download/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { RealInstagramDownloader } from '@/lib/real-instagram-downloader';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { url } = await request.json();\n\n    if (!url) {\n      return NextResponse.json(\n        { error: 'URL es requerida' },\n        { status: 400 }\n      );\n    }\n\n    // Validar que sea una URL de Instagram\n    if (!RealInstagramDownloader.isValidInstagramUrl(url)) {\n      return NextResponse.json(\n        { error: 'URL de Instagram no válida' },\n        { status: 400 }\n      );\n    }\n\n    try {\n      const result = await RealInstagramDownloader.downloadVideo(url);\n\n      if (result.success && result.data) {\n        return NextResponse.json({\n          type: 'video',\n          qualities: [\n            {\n              url: result.data.videoUrl,\n              quality: result.data.quality,\n            }\n          ],\n          thumbnail: result.data.thumbnail,\n          caption: result.data.title || 'Video de Instagram',\n        });\n      } else {\n        return NextResponse.json(\n          { error: result.error || 'No se pudo extraer el video de Instagram' },\n          { status: 404 }\n        );\n      }\n    } catch (error: any) {\n      console.error('Error al descargar video:', error);\n      return NextResponse.json(\n        { error: 'Error al procesar el video. Inténtalo de nuevo más tarde.' },\n        { status: 503 }\n      );\n    }\n\n  } catch (error: any) {\n    console.error('Error en la API:', error);\n    return NextResponse.json(\n      { error: 'Error interno del servidor' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,QAAQ,IAAI;QAElC,IAAI,CAAC,KAAK;YACR,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,uCAAuC;QACvC,IAAI,CAAC,+IAAA,CAAA,0BAAuB,CAAC,mBAAmB,CAAC,MAAM;YACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6B,GACtC;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,+IAAA,CAAA,0BAAuB,CAAC,aAAa,CAAC;YAE3D,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,MAAM;oBACN,WAAW;wBACT;4BACE,KAAK,OAAO,IAAI,CAAC,QAAQ;4BACzB,SAAS,OAAO,IAAI,CAAC,OAAO;wBAC9B;qBACD;oBACD,WAAW,OAAO,IAAI,CAAC,SAAS;oBAChC,SAAS,OAAO,IAAI,CAAC,KAAK,IAAI;gBAChC;YACF,OAAO;gBACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO,OAAO,KAAK,IAAI;gBAA2C,GACpE;oBAAE,QAAQ;gBAAI;YAElB;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4D,GACrE;gBAAE,QAAQ;YAAI;QAElB;IAEF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA6B,GACtC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}